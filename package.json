{"name": "mailpallet-webapp", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy:functions": "supabase functions deploy", "deploy:web": "npm run build && firebase deploy", "deploy": "npm run deploy:web && npm run deploy:functions", "deploy:fb-functions": "cd functions && npm install && npm run build && cd .. && firebase deploy --only functions"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.9.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.0.0", "embla-carousel-autoplay": "^8.3.1", "embla-carousel-react": "^8.3.1", "firebase-functions": "^6.2.0", "framer-motion": "^11.11.10", "gray-matter": "^4.0.3", "lucide-react": "^0.452.0", "next": "^14.2.22", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "recharts": "^2.15.1", "rehype-stringify": "^10.0.1", "resend": "^4.0.1", "stripe": "^17.2.1", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "ignore-loader": "^0.1.2", "postcss": "^8", "supabase": "^1.207.9", "tailwindcss": "^3.4.1", "typescript": "^5"}}