import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import {
  requireAdmin,
  createUnauthorizedResponse,
  createForbiddenResponse,
} from "@/lib/auth-helpers";

/**
 * GET /api/products
 * Query params:
 *   - id: string (required)
 *   - includeArchived: boolean (optional, default: true)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    const id = decodeURIComponent(searchParams.get("id") || "");
    const includeArchivedParam = searchParams.get("includeArchived");
    const includeArchived =
      includeArchivedParam === null
        ? true
        : includeArchivedParam === "true" || includeArchivedParam === "1";

    if (!id) {
      return NextResponse.json(
        { error: "Product id is required." },
        { status: 400 },
      );
    }

    let query = supabase.from("mp_ecommerce_products").select("*").eq("id", id);

    if (!includeArchived) {
      query = query.eq("archived", false);
    }

    const { data, error } = await query.single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching product.", details: error.message },
        { status: 404 },
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}

/**
 * POST /api/products
 * Body: Product object with required fields
 */
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication for product creation
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();

    // Validate required fields
    const {
      title,
      description,
      primary_image,
      primary_data,
      currency,
      type,
      category,
      subcategory,
    } = body;

    if (
      !title ||
      !description ||
      !primary_image ||
      !primary_data ||
      !currency ||
      !type ||
      !category ||
      !subcategory
    ) {
      return NextResponse.json(
        {
          error:
            "Required fields missing. Need: title, description, primary_image, primary_data, currency, type, category, subcategory",
        },
        { status: 400 },
      );
    }

    // Insert product into database
    const { data, error } = await supabase
      .from("mp_ecommerce_products")
      .insert([body])
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error creating product.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}

/**
 * PATCH /api/products
 * Body: Product object with id and fields to update
 */
export async function PATCH(request: NextRequest) {
  try {
    // Require admin authentication for product updates
    try {
      await requireAdmin();
    } catch (error) {
      return createUnauthorizedResponse("Admin access required");
    }

    const supabase = await createClient();
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Product id is required for update." },
        { status: 400 },
      );
    }

    // Add updated_at timestamp
    updateData.updated_at = new Date().toISOString();

    // Update product in database
    const { data, error } = await supabase
      .from("mp_ecommerce_products")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error updating product.", details: error.message },
        { status: 500 },
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: "Product not found." },
        { status: 404 },
      );
    }

    return NextResponse.json({ data }, { status: 200 });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
