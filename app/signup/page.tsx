"use client";

import { z } from "zod";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import Header from "@/components/index/header";
import { Button } from "@/components/ui/button";
import Branding from "@/components/app/branding";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { RegisterModel, UserService } from "@/services/user.service";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectGroup, SelectLabel, SelectItem } from "@/components/ui/select";
import { countryList } from "@/lib/constants";

const formSchema = z
  .object({
    firstName: z.string().min(1, {
      message: "First name is required",
    }),
    lastName: z.string().min(1, {
      message: "Last name is required",
    }),
    dialCode: z.string().min(1, {
      message: "Country is required",
    }),
    phoneNumber: z.string().min(1, {
      message: "Phone number is required",
    }),
    emailAddress: z.string().email({
      message: "Email address must be a valid email address",
    }).transform((val) => val.toLowerCase()),
    password: z.string().min(6, {
      message: "Password must be at least 6 characters",
    }),
    confirmPassword: z.string().min(6, {
      message: "Confirm password must be at least 6 characters",
    }),
    receiveMarketingInfo: z.boolean(),
    agreeToPrivacyPolicy: z.boolean().refine((value) => value === true, {
      message: "You must agree to the privacy policy",
    }),
    agreeShippingToPolicy: z.boolean().refine((value) => value === true, {
      message: "You must agree to the shipping policy",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export default function Signup() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      dialCode: "",
      phoneNumber: "",
      emailAddress: "",
      password: "",
      confirmPassword: "",
      receiveMarketingInfo: false,
      agreeToPrivacyPolicy: false,
      agreeShippingToPolicy: false,
    },
  });

  const getFormData = (values: z.infer<typeof formSchema>) => {
    let data: RegisterModel = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.emailAddress,
      password: values.password,
      countryCode: values.dialCode,
      mobileNumber: values.phoneNumber,
      confirmPassword: values.confirmPassword,
      receiveMarketingInfo: values.receiveMarketingInfo,
      agreeToPrivacyPolicy: values.agreeToPrivacyPolicy,
      agreeToShippingPolicy: values.agreeShippingToPolicy,
    };
    return data;
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    const registerModel = getFormData(values);
    const userService = new UserService();
    const result = await userService.createAccount(registerModel);
    if (typeof result === "string") {
      setLoading(false);
      toast({
        title: "Oops!",
        description: result,
        variant: "destructive",
      });
    }
    if (result === true) {
      window.location.href = "/welcome";
    }
  };

const countryList = [
  { label: "Botswana (+267)", value: "+267" },
  { label: "Egypt (+20)", value: "+20" },
  { label: "Ghana (+233)", value: "+233" },
  { label: "India (+91)", value: "+91" },
  { label: "Indonesia (+62)", value: "+62" },
  { label: "Kenya (+254)", value: "+254" },
  { label: "Kuwait (+965)", value: "+965" },
  { label: "Liberia (+231)", value: "+231" },
  { label: "Malawi (+265)", value: "+265" },
  { label: "Malaysia (+60)", value: "+60" },
  { label: "Namibia (+264)", value: "+264" },
  { label: "Nigeria (+234)", value: "+234" },
  { label: "Pakistan (+92)", value: "+92" },
  { label: "Philippines (+63)", value: "+63" },
  { label: "Rwanda (+250)", value: "+250" },
  { label: "Saudi Arabia (+966)", value: "+966" },
  { label: "Sierra Leone (+232)", value: "+232" },
  { label: "Singapore (+65)", value: "+65" },
  { label: "South Africa (+27)", value: "+27" },
  { label: "South Sudan (+211)", value: "+211" },
  { label: "Tanzania (+255)", value: "+255" },
  { label: "Thailand (+66)", value: "+66" },
  { label: "UAE (+971)", value: "+971" },
  { label: "United Kingdom (+44)", value: "+44" },
  { label: "Zambia (+260)", value: "+260" },
  { label: "Zimbabwe (+263)", value: "+263" },
] as const;

  return (
    <div>
      <Header />
      <div className="w-full min-h-screen lg:grid lg:grid-cols-2">
        {/* Left column with image - hidden on mobile */}
        <div className="hidden lg:block">
          <Branding />
        </div>

        {/* Right column with form */}
        <div className="flex flex-col items-center justify-center min-h-screen p-4 lg:p-6">
          {/* Mobile logo and text */}
          <div className="mb-8 text-center lg:hidden">
            <Image src="/assets/imgs/logo.svg" alt="Logo" width={100} height={100} className="mx-auto mb-2" />
            <p className="text-lg font-semibold">Shop it, We Ship it</p>
          </div>

          <div className="w-full max-w-[350px] space-y-6">
            <div className="grid gap-2 text-center">
              <h1 className="text-2xl font-bold sm:text-3xl">Create Account</h1>
              <p className="text-sm text-muted-foreground sm:text-base">Enter your details below to sign up</p>
            </div>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid gap-4">
                  {/* Name Fields */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Max" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Robinson" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Email Field */}
                  <FormField
                    control={form.control}
                    name="emailAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Country and Phone Fields */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="dialCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Country</FormLabel>
                          <FormControl>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select Country" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  <SelectLabel>Countries</SelectLabel>
                                  {countryList.map((country) => (
                                    <SelectItem key={country.value} value={country.value}>
                                      {country.label}
                                    </SelectItem>
                                  ))}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input placeholder="0541000000" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Password Fields */}
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="••••••••" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm Password</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="••••••••" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Checkbox Fields */}
                  <FormField
                    control={form.control}
                    name="receiveMarketingInfo"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center space-x-2 mt-4">
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            <FormLabel className="text-xs sm:text-sm">Receive shipping updates and offers.</FormLabel>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="agreeToPrivacyPolicy"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center space-x-2">
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            <FormLabel className="text-xs sm:text-sm">
                              I agree to the{" "}
                              <Link href="/legal/privacy-policy" target="_blank" className="text-primary font-semibold underline">
                                privacy policy
                              </Link>
                              .
                            </FormLabel>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="agreeShippingToPolicy"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center space-x-2">
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            <FormLabel className="text-xs sm:text-sm">
                              I agree to the{" "}
                              <Link href="/legal/shipping-policy" target="_blank" className="text-primary font-semibold underline">
                                shipping policy
                              </Link>
                              .
                            </FormLabel>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Submit Button */}
                <Button type="submit" className="w-full mt-12" disabled={loading}>
                  {loading ? "Please wait..." : "Create account"}
                </Button>
              </form>
            </Form>
            <div className="mt-4 text-center text-sm">
              Already have an account?{" "}
              <Link href="/login" className="underline">
                Log in
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
