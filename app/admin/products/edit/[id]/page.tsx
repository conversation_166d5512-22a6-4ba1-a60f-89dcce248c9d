"use client";

import { ProductService } from "@/services/product.service.ts";
import {
  Product,
  ProductVariant,
  ExternalProductDetails,
  InternalProductDetails,
  ProductShippingRate,
  ProductCreationData,
  ProductUpdateData,
} from "@/data/models/product.model.ts";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import ReactMarkdown from "react-markdown";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { z } from "zod";
import {
  Package2,
  DollarSign,
  ImagePlus,
  Ship,
  Settings,
  Tag,
  Info,
  MapPin,
  Percent,
  Edit,
  CheckIcon,
  ChevronsUpDownIcon,
  Truck,
} from "lucide-react";
import {
  Category,
  CategoryService,
  CategoryVariantAttribute,
} from "@/services/category.service";
import { cn } from "@/lib/utils";
import { categories } from "@/lib/constants";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const shippingRateSchema = z.object({
  to_country: z.enum(["UK", "Ghana", "Nigeria", "Rest Of Africa"]),
  to_zone: z.enum(["UK", "Accra", "Outside Accra", "Lagos", "Outside Lagos"]),
  base_rate: z.number().optional(),
  duty: z.number().optional(),
  rate_per_kg: z.number().min(0).optional(),
  estimated_delivery_days: z.number().min(0).optional(),
  currency: z.string(),
});

const externalDetailsSchema = z.object({
  origin_name: z.string().min(1),
  origin_url: z.string().url(),
  is_affiliate: z.boolean(),
  discount_code: z.string().optional(),
});

const internalDetailsSchema = z.object({
  location: z.enum(["UK", "Ghana"]),
  weight_kg: z.number().optional(),
  dimensions_cm: z
    .object({
      length: z.number().optional(),
      width: z.number().optional(),
      height: z.number().optional(),
    })
    .optional(),
});

// Individual variant item schema - matches the structure in ProductVariant.variants array
const variantItemSchema = z
  .object({
    option_values: z.record(z.string()), // The selected values for each attribute
    sale_price: z.number().optional(),
    price: z.number().optional(),
    stock: z.number().optional(),
    currency: z.string().optional(),
    images: z
      .array(
        z.object({
          url: z.string(),
          name: z.string(),
        }),
      )
      .optional(),
  })
  .passthrough(); // Allow additional fields

// Master option schema - matches ProductVariant.master_options structure
const masterOptionSchema = z.record(z.array(z.string()));

// Complete variants schema that matches ProductVariant interface
const productVariantsSchema = z
  .object({
    variants: z.array(variantItemSchema),
    master_options: z.array(masterOptionSchema),
  })
  .optional();

const formSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().min(1, { message: "Description is required" }),
  sale_price: z
    .number()
    .min(0, { message: "Sale price must be greater than or equal to 0" })
    .optional(),
  price: z.number().min(1, { message: "Price must be greater than 0" }),
  discount_percent: z.number().min(0).max(100).optional(),
  currency: z.string().min(1),
  type: z.enum(["external", "internal"]),
  category: z.string().min(1),
  subcategory: z.string().min(1),
  brand: z.string().optional(),
  tags: z.array(z.string()).optional(),
  condition: z.enum(["New", "Used"]),
  admin_notes: z.string().optional(),
  origin_location: z.enum(["UK", "Ghana"]),
  shipping_rates: z.array(shippingRateSchema),
  details: z.union([externalDetailsSchema, internalDetailsSchema]),
  refundable: z.boolean(),
  refund_policy: z.string().min(1, { message: "Refund policy is required" }),
  variants: productVariantsSchema,
});

type FormValues = z.infer<typeof formSchema>;

const productService = new ProductService();
const categoryService = new CategoryService();

const refundPolicyMsg = {
  true: "Item is refundable. Contact our support.",
  false: "Item is non refundable. Contact our support for any issues.",
};

export default function EditProductPage({
  params,
}: {
  params: { id: string };
}) {
  const productId = params.id;
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [productData, setProductData] = useState<Product | null>(null);
  const [primaryImage, setPrimaryImage] = useState<File | null>(null);
  const [primaryImagePreview, setPrimaryImagePreview] = useState<string>("");
  const [sharedImages, setSharedImages] = useState<File[]>([]);
  const [sharedImagePreviews, setSharedImagePreviews] = useState<string[]>([]);
  const [removedSharedImageNames, setRemovedSharedImageNames] = useState<
    string[]
  >([]);

  // Variant-related state
  const [variantAttributes, setVariantAttributes] = useState<
    CategoryVariantAttribute[]
  >([]);
  const [variantImages, setVariantImages] = useState<{
    [variantIndex: number]: File[];
  }>({});
  const [variantImagePreviews, setVariantImagePreviews] = useState<{
    [variantIndex: number]: string[];
  }>({});
  const [variantIdsToDelete, setVariantIdsToDelete] = useState<string[]>([]);
  const [removedVariantImageNames, setRemovedVariantImageNames] = useState<
    string[]
  >([]);

  // State for custom variant attribute values
  const [customAttributeValues, setCustomAttributeValues] = useState<
    Array<{
      variantIndex: number;
      attribute: string;
      value: string;
    }>
  >([]);

  // Helper functions for custom attribute values
  const getCustomAttributeValue = (
    variantIndex: number,
    attribute: string,
  ): string => {
    const customValue = customAttributeValues.find(
      (item) =>
        item.variantIndex === variantIndex && item.attribute === attribute,
    );
    return customValue?.value || "";
  };

  const setCustomAttributeValue = (
    variantIndex: number,
    attribute: string,
    value: string,
  ) => {
    setCustomAttributeValues((prev) => {
      const filtered = prev.filter(
        (item) =>
          !(item.variantIndex === variantIndex && item.attribute === attribute),
      );
      if (value.trim()) {
        return [...filtered, { variantIndex, attribute, value }];
      }
      return filtered;
    });
  };

  // Brand-related state
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);

  const getDefaultValues = () => ({
    title: "",
    description: "",
    sale_price: undefined,
    price: 1,
    discount_percent: undefined,
    currency: "GBP",
    type: "internal" as const,
    category: "",
    subcategory: "",
    brand: "none",
    tags: [],
    condition: "New" as const,
    admin_notes: "",
    origin_location: "UK" as const,
    shipping_rates: [
      {
        to_country: "UK" as const,
        to_zone: "UK" as const,
        base_rate: 0,
        currency: "GBP",
      },
    ],
    details: {
      location: "UK" as const,
    },
    refundable: false,
    refund_policy: "",
    variants: undefined,
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  });

  const {
    fields: shippingRateFields,
    append: appendShippingRate,
    remove: removeShippingRate,
  } = useFieldArray({
    control: form.control,
    name: "shipping_rates",
  });

  useEffect(() => {
    if (productId) {
      const fetchProductAndResetForm = async () => {
        setIsLoading(true);
        try {
          const data = await productService.getProduct(productId);
          setProductData(data);
          setVariantIdsToDelete([]); // Reset delete list when loading product

          // Set primary image preview if exists
          if (data.primary_image?.url) {
            setPrimaryImagePreview(data.primary_image.url);
          }

          // Set shared image previews if they exist
          if (data.shared_images && data.shared_images.length > 0) {
            setSharedImagePreviews(data.shared_images.map((img) => img.url));
          }

          // Initialize variant images and previews from product variants
          const initialVariantImages: { [variantIndex: number]: File[] } = {};
          const initialVariantImagePreviews: {
            [variantIndex: number]: string[];
          } = {};

          if (data.variants?.variants) {
            data.variants.variants.forEach((variant, index) => {
              if (variant.images && variant.images.length > 0) {
                initialVariantImagePreviews[index] = variant.images.map(
                  (img) => img.url,
                );
                // For existing images, we don't have File objects, so we keep empty arrays for files
                initialVariantImages[index] = [];
              }
            });
          }

          setVariantImages(initialVariantImages);
          setVariantImagePreviews(initialVariantImagePreviews);
          setRemovedSharedImageNames([]);
          setRemovedVariantImageNames([]);

          // Extract custom attribute values from existing product data
          const extractedCustomValues: Array<{
            variantIndex: number;
            attribute: string;
            value: string;
          }> = [];

          if (data.variants?.variants) {
            data.variants.variants.forEach((variant, variantIndex) => {
              if (variant.option_values) {
                Object.entries(variant.option_values).forEach(
                  ([attribute, value]) => {
                    // Check if this is a custom value (object with is_custom flag)
                    const optionValue = value as any;
                    if (
                      typeof optionValue === "object" &&
                      optionValue?.is_custom === true &&
                      optionValue?.value
                    ) {
                      extractedCustomValues.push({
                        variantIndex,
                        attribute,
                        value: optionValue.value,
                      });
                    }
                  },
                );
              }
            });
          }

          setCustomAttributeValues(extractedCustomValues);

          // Transform variant data for form: convert custom objects back to "Custom" strings
          const transformedVariants = data.variants
            ? {
                ...data.variants,
                variants: data.variants.variants?.map((variant) => ({
                  ...variant,
                  option_values: variant.option_values
                    ? Object.fromEntries(
                        Object.entries(variant.option_values).map(
                          ([key, value]) => {
                            // Convert custom objects back to "Custom" string for form
                            const optionValue = value as any;
                            if (
                              typeof optionValue === "object" &&
                              optionValue?.is_custom === true
                            ) {
                              return [key, "Custom"];
                            }
                            return [key, value];
                          },
                        ),
                      )
                    : {},
                })),
              }
            : undefined;

          form.reset({
            title: data.title,
            description: data.description,
            sale_price: data.primary_data.sale_price,
            price: data.primary_data.price,
            discount_percent: data.primary_data.discount_percent,
            currency: data.currency,
            category: data.category,
            subcategory: data.subcategory,
            brand: data.brand || "none",
            tags: data.tags ?? [],
            condition: data.condition,
            admin_notes: data.admin_notes ?? "",
            origin_location: data.origin_location,
            type: data.type,
            shipping_rates: data.shipping_rates.map((sr) => ({
              ...sr,
              rate_per_kg: sr.rate_per_kg ?? undefined,
            })),
            details: data.details,
            refundable: data.refundable,
            refund_policy: data.refund_policy,
            variants: transformedVariants,
          });

          // Fetch brands for the current subcategory
          if (data.subcategory) {
            try {
              const brands = await categoryService.getCategoryBrands(
                data.subcategory,
              );
              setAvailableBrands(brands || []);
            } catch (error) {
              console.error("Error fetching brands:", error);
              setAvailableBrands([]);
            }
          }

          // Fetch categories
          try {
            const categoriesData = await categoryService.getCategories();
            setCategories(categoriesData);
          } catch (error) {
            console.error("Error fetching categories:", error);
            toast({
              title: "Error",
              description: "Failed to load categories",
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Failed to fetch product data for form reset:", error);
          toast({
            title: "Error",
            description: "Failed to load product data for editing.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      };
      fetchProductAndResetForm();
    }
  }, [productId, form, toast]);

  const handlePrimaryImageChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Clean up previous primary image preview
      if (primaryImagePreview) {
        URL.revokeObjectURL(primaryImagePreview);
      }

      setPrimaryImage(file);
      setPrimaryImagePreview(URL.createObjectURL(file));
    }
  };

  const removePrimaryImage = () => {
    if (primaryImagePreview) {
      URL.revokeObjectURL(primaryImagePreview);
    }
    setPrimaryImage(null);
    setPrimaryImagePreview("");
  };

  // Shared image handling functions
  const handleSharedImagesChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    setSharedImages((prev) => [...prev, ...files]);

    // Create preview URLs for the images
    const newPreviewUrls = files.map((file) => URL.createObjectURL(file));
    setSharedImagePreviews((prev) => [...prev, ...newPreviewUrls]);
  };

  const removeSharedImage = (imageIndex: number) => {
    const urlToRemove = sharedImagePreviews[imageIndex];
    const existingImg = productData?.shared_images?.find(
      (img) => img.url === urlToRemove,
    );

    if (existingImg) {
      setRemovedSharedImageNames((prev) => [...prev, existingImg.name]);
    } else {
      // Determine index among newly added images only
      const isExisting = (u: string) =>
        !!productData?.shared_images?.some((img) => img.url === u);
      const newIndex = sharedImagePreviews
        .slice(0, imageIndex)
        .filter((u) => !isExisting(u)).length; // position within new images

      setSharedImages((prev) => prev.filter((_, i) => i !== newIndex));
    }

    setSharedImagePreviews((prev) => {
      const url = prev[imageIndex];
      if (url && url.startsWith("blob:")) {
        URL.revokeObjectURL(url);
      }
      return prev.filter((_, i) => i !== imageIndex);
    });
  };

  // Variant image handling functions
  const handleVariantImagesChange = (
    variantIndex: number,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    setVariantImages((prev) => ({
      ...prev,
      [variantIndex]: [...(prev[variantIndex] || []), ...files],
    }));

    // Create preview URLs for the images
    const newPreviewUrls = files.map((file) => URL.createObjectURL(file));
    setVariantImagePreviews((prev) => ({
      ...prev,
      [variantIndex]: [...(prev[variantIndex] || []), ...newPreviewUrls],
    }));
  };

  const removeVariantImage = (variantIndex: number, imageIndex: number) => {
    const previewsForVariant = variantImagePreviews[variantIndex] || [];
    const urlToRemove = previewsForVariant[imageIndex];
    const existingVariantImages =
      productData?.variants?.variants?.[variantIndex]?.images || [];
    const existingImg = existingVariantImages.find(
      (img) => img.url === urlToRemove,
    );

    if (existingImg) {
      setRemovedVariantImageNames((prev) => [...prev, existingImg.name]);
    } else {
      const existingUrls = new Set(existingVariantImages.map((img) => img.url));
      const newIndex = previewsForVariant
        .slice(0, imageIndex)
        .filter((u) => !existingUrls.has(u)).length;
      setVariantImages((prev) => ({
        ...prev,
        [variantIndex]: (prev[variantIndex] || []).filter(
          (_, i) => i !== newIndex,
        ),
      }));
    }

    setVariantImagePreviews((prev) => {
      const currentPreviews = prev[variantIndex] || [];
      const url = currentPreviews[imageIndex];
      if (url && url.startsWith("blob:")) {
        URL.revokeObjectURL(url);
      }
      return {
        ...prev,
        [variantIndex]: currentPreviews.filter((_, i) => i !== imageIndex),
      };
    });
  };

  const addVariant = () => {
    const currentVariants = form.getValues("variants");

    // Create option_values object for this variant
    const option_values: Record<string, string> = {};
    variantAttributes.forEach((attr) => {
      const defaultValue =
        attr.values && attr.values.length > 0 ? attr.values[0] : "";
      option_values[attr.name] = String(defaultValue);
    });

    const newVariantItem = {
      option_values,
      sale_price: 0,
      price: 0,
      stock: 0,
      currency: "GBP",
    };

    if (currentVariants) {
      // Update existing variants structure
      const updatedVariants = {
        ...currentVariants,
        variants: [...(currentVariants.variants || []), newVariantItem],
      };
      form.setValue("variants", updatedVariants);
    } else {
      // Create new variants structure
      const newVariants = {
        variants: [newVariantItem],
        master_options: variantAttributes.map((attr) => ({
          [attr.name]: (attr.values || []).map(String),
        })),
      };
      form.setValue("variants", newVariants);
    }
  };

  const removeVariant = (index: number) => {
    const currentVariants = form.getValues("variants");
    if (currentVariants && currentVariants.variants) {
      const variantToRemove = currentVariants.variants[index];

      // If the variant has an ID, it's an existing variant - add it to delete list
      if (variantToRemove?.id) {
        setVariantIdsToDelete((prev) => [...prev, String(variantToRemove.id)]);
      }

      // Track existing images on this variant for deletion from storage
      const existingImages =
        productData?.variants?.variants?.[index]?.images || [];
      if (existingImages.length > 0) {
        setRemovedVariantImageNames((prev) => [
          ...prev,
          ...existingImages.map((img) => img.name),
        ]);
      }

      // Remove variant from form
      const updatedVariants = {
        ...currentVariants,
        variants: currentVariants.variants.filter(
          (_: any, i: number) => i !== index,
        ),
      };
      form.setValue("variants", updatedVariants);
    }

    // Clean up variant images and previews
    const currentVariantPreviews = variantImagePreviews[index] || [];

    currentVariantPreviews.forEach((url) => URL.revokeObjectURL(url));

    setVariantImages((prev) => {
      const newState = { ...prev };
      delete newState[index];
      return newState;
    });

    setVariantImagePreviews((prev) => {
      const newState = { ...prev };
      delete newState[index];
      return newState;
    });

    // Clean up custom attribute values for this variant
    setCustomAttributeValues((prev) =>
      prev.filter((item) => item.variantIndex !== index),
    );
  };

  const onSubmit = async (formData: FormValues) => {
    if (!productData) {
      toast({
        title: "Error",
        description: "Original product data not loaded. Cannot update.",
        variant: "destructive",
      });
      return;
    }

    if (!primaryImage && !primaryImagePreview) {
      toast({
        title: "Error",
        description: "Please upload a primary product image",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Prepare variants with custom value transformation and proper master_options
      let preparedVariants = formData.variants;

      if (preparedVariants && preparedVariants.variants) {
        // 1. Transform custom values from string to object format
        const variantsWithTransformedCustomValues =
          preparedVariants.variants.map((variant, variantIndex) => {
            const transformedOptionValues: Record<string, any> = {};

            Object.entries(variant.option_values).forEach(([key, value]) => {
              if (typeof value === "string") {
                if (value === "Custom") {
                  // Check if we have a custom value for this attribute in our custom state
                  const customValue = getCustomAttributeValue(
                    variantIndex,
                    key,
                  );
                  if (customValue.trim()) {
                    transformedOptionValues[key] = {
                      value: customValue,
                      is_custom: true,
                    };
                  } else {
                    // No custom value provided, keep as "Custom" (this shouldn't happen in normal flow)
                    transformedOptionValues[key] = value;
                  }
                } else {
                  // Regular predefined value
                  transformedOptionValues[key] = value;
                }
              } else {
                // Already in object format (shouldn't happen with new logic, but handle for safety)
                transformedOptionValues[key] = value;
              }
            });

            return {
              ...variant,
              option_values: transformedOptionValues,
            };
          });

        // 2. Create master_options based on actual option_values in variants
        const master_options: Array<Record<string, Array<string>>> = [];

        if (variantAttributes.length > 0) {
          variantAttributes.forEach((attr) => {
            // Get all unique values for this attribute from actual variants
            const valuesInVariants = new Set<string>();
            variantsWithTransformedCustomValues.forEach((variant) => {
              if (variant.option_values[attr.name]) {
                const optionValue = variant.option_values[attr.name] as any;

                if (typeof optionValue === "object" && optionValue?.value) {
                  if (optionValue.is_custom) {
                    // Custom value - add the actual custom value to master_options
                    valuesInVariants.add(optionValue.value);
                  } else {
                    // Object format for non-custom values
                    valuesInVariants.add(optionValue.value);
                  }
                } else if (
                  typeof optionValue === "string" &&
                  optionValue !== "Custom"
                ) {
                  // String format for regular predefined values
                  valuesInVariants.add(optionValue);
                }
                // Skip "Custom" string as it's just a placeholder
              }
            });

            // Convert Set to Array and only include if there are actual values used
            const actualValues = Array.from(valuesInVariants);

            // Only add to master_options if there are actual values used in variants
            if (actualValues.length > 0) {
              master_options.push({
                [attr.name]: actualValues,
              });
            }
          });
        }

        preparedVariants = {
          variants: variantsWithTransformedCustomValues,
          master_options,
        };
      }

      // Extract product data without the pricing fields that go into primary_data
      const {
        sale_price,
        price,
        discount_percent,
        currency,
        variants,
        ...productDataForUpdate
      } = formData;

      // Transform brand value: convert "none" to undefined for database storage
      const transformedProductData = {
        ...productDataForUpdate,
        brand:
          productDataForUpdate.brand === "none"
            ? undefined
            : productDataForUpdate.brand,
      };

      // Create the update payload
      const updatePayload: ProductUpdateData = {
        productUpdates: {
          ...transformedProductData,
          primary_data: {
            sale_price: formData.sale_price || 0,
            price: formData.price,
            discount_percent: formData.discount_percent,
          },
          currency: formData.currency,
          variants: preparedVariants,
          updated_at: new Date(),
        },
        primaryImage: primaryImage || undefined,
        shared_images: sharedImages,
        variant_images: variantImages,
        removed_shared_image_names: removedSharedImageNames,
        removed_variant_image_names: removedVariantImageNames,
      };

      await productService.updateProduct(productId, updatePayload);

      toast({
        title: "Success",
        description: "Product updated successfully",
      });

      router.push("/admin/products/edit");
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update product",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const productType = form.watch("type");
  const price = form.watch("price");
  const salePrice = form.watch("sale_price");
  const refundable = form.watch("refundable");

  useEffect(() => {
    // When type changes, reset the details field with appropriate structure
    const currentDetails = form.getValues("details");

    if (productType === "external") {
      // Check if current details has external structure
      if (!currentDetails || !("origin_name" in currentDetails)) {
        form.setValue("details", {
          origin_name: "",
          origin_url: "",
          is_affiliate: false,
          discount_code: "",
        });
      }
    } else {
      // Check if current details has internal structure
      if (!currentDetails || !("location" in currentDetails)) {
        form.setValue("details", {
          location: "UK",
          weight_kg: undefined,
          dimensions_cm: undefined,
        });
      }
    }
  }, [productType, form]);

  // Calculate discount percentage when price and sale_price change
  useEffect(() => {
    // Only calculate if both price and sale_price have valid values
    if (
      typeof price === "number" &&
      typeof salePrice === "number" &&
      price > 0 &&
      salePrice >= 0
    ) {
      // Only calculate discount if sale_price is less than price
      if (salePrice < price) {
        const discountPercent = ((price - salePrice) / price) * 100;
        // Round to 2 decimal places
        const roundedDiscount = Math.round(discountPercent * 100) / 100;
        form.setValue("discount_percent", roundedDiscount);
      } else {
        // If sale_price is greater than or equal to price, clear discount
        form.setValue("discount_percent", undefined);
      }
    } else if (salePrice === undefined || salePrice === null) {
      // If sale_price is cleared, clear the discount percentage
      form.setValue("discount_percent", undefined);
    }
  }, [price, salePrice, form]);

  // Update refund_policy based on refundable status
  useEffect(() => {
    const message = refundable ? refundPolicyMsg.true : refundPolicyMsg.false;
    form.setValue("refund_policy", message);
  }, [refundable, form]);

  // Watch for subcategory changes to fetch variant attributes
  const selectedSubcategory = form.watch("subcategory");

  useEffect(() => {
    const fetchCategoryData = async () => {
      if (selectedSubcategory) {
        try {
          // Fetch variant attributes
          const attributes =
            await categoryService.getCategoryVariantAttributes(
              selectedSubcategory,
            );
          setVariantAttributes(attributes || []);

          // Fetch brands
          const brands =
            await categoryService.getCategoryBrands(selectedSubcategory);
          setAvailableBrands(brands || []);

          // Only reset variants and brand when subcategory changes if we're not loading initial product data
          // and if the product data has already been loaded (to prevent clearing during initial load)
          if (!isLoading && productData) {
            const currentVariants = form.getValues("variants");
            // Only clear if there are no existing variants or if this is a manual subcategory change
            if (
              !currentVariants ||
              (currentVariants.variants &&
                currentVariants.variants.length === 0)
            ) {
              form.setValue("variants", undefined);
              setVariantImages({});
              setVariantImagePreviews({});
              setCustomAttributeValues([]);
            }
            // Reset brand when subcategory changes manually
            form.setValue("brand", "none");
          }
        } catch (error) {
          console.error("Error fetching category data:", error);
          setVariantAttributes([]);
          setAvailableBrands([]);
        }
      } else {
        setVariantAttributes([]);
        setAvailableBrands([]);
        // Only clear variants and brand if we're not in the middle of loading product data
        if (!isLoading && productData) {
          form.setValue("variants", undefined);
          form.setValue("brand", "none");
          setVariantImages({});
          setVariantImagePreviews({});
          setCustomAttributeValues([]);
        }
      }
    };

    fetchCategoryData();
  }, [selectedSubcategory, categoryService, form, isLoading, productData]);

  if (isLoading && !productData) {
    return (
      <h1 className="m-8 text-3xl font-bold tracking-tight">
        Fetching Product Data...
      </h1>
    );
  }

  if (!productData && !isLoading) {
    return (
      <h1 className="m-8 text-3xl font-bold tracking-tight">
        Product not found or failed to load.
      </h1>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="max-w-7xl mx-auto p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
            <p className="text-muted-foreground mt-2">
              Update product details, pricing, and shipping information
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 items-start">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package2 className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Product title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Markdown Supported)</FormLabel>
                      <FormControl>
                        <Tabs defaultValue="write" className="w-full">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="write">Write</TabsTrigger>
                            <TabsTrigger value="preview">Preview</TabsTrigger>
                          </TabsList>
                          <TabsContent value="write">
                            <Textarea
                              placeholder="Product description (supports markdown: **bold**, *italic*, `code`, etc.)"
                              className="min-h-[150px]"
                              {...field}
                            />
                          </TabsContent>
                          <TabsContent value="preview">
                            <div className="min-h-[150px] p-3 border rounded-md bg-muted/50">
                              {field.value ? (
                                <div className="prose prose-sm max-w-none dark:prose-invert">
                                  <ReactMarkdown>{field.value}</ReactMarkdown>
                                </div>
                              ) : (
                                <div className="text-muted-foreground text-sm">
                                  Nothing to preview
                                </div>
                              )}
                            </div>
                          </TabsContent>
                        </Tabs>
                      </FormControl>
                      <FormDescription>
                        You can use markdown formatting. Switch to Preview tab
                        to see how it will look.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="internal">Internal</SelectItem>
                            <SelectItem value="external">External</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="condition"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Condition</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select condition" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="New">New</SelectItem>
                            <SelectItem value="Used">Used</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => {
                      const [open, setOpen] = React.useState(false);
                      const parentCategories = categories.filter(
                        (cat) => cat.parent_id === null,
                      );

                      return (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between"
                                >
                                  {field.value
                                    ? parentCategories.find(
                                        (category) =>
                                          category.id === field.value,
                                      )?.name
                                    : "Select category..."}
                                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput placeholder="Search category..." />
                                <CommandList>
                                  <CommandEmpty>
                                    No category found.
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {parentCategories.map((category) => (
                                      <CommandItem
                                        key={category.id}
                                        value={category.name}
                                        onSelect={() => {
                                          field.onChange(category.id);
                                          // Clear subcategory when category changes
                                          form.setValue("subcategory", "");
                                          setOpen(false);
                                        }}
                                      >
                                        <CheckIcon
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === category.id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {category.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="subcategory"
                    render={({ field }) => {
                      const [open, setOpen] = React.useState(false);
                      const selectedCategoryId = form.watch("category");
                      const subcategories = categories.filter(
                        (cat) => cat.parent_id === selectedCategoryId,
                      );

                      return (
                        <FormItem>
                          <FormLabel>Subcategory</FormLabel>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between"
                                  disabled={!selectedCategoryId}
                                >
                                  {field.value
                                    ? subcategories.find(
                                        (subcategory) =>
                                          subcategory.id === field.value,
                                      )?.name
                                    : selectedCategoryId
                                      ? "Select subcategory..."
                                      : "Select category first"}
                                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput placeholder="Search subcategory..." />
                                <CommandList>
                                  <CommandEmpty>
                                    No subcategory found.
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {subcategories.map((subcategory) => (
                                      <CommandItem
                                        key={subcategory.id}
                                        value={subcategory.name}
                                        onSelect={() => {
                                          field.onChange(subcategory.id);
                                          setOpen(false);
                                        }}
                                      >
                                        <CheckIcon
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === subcategory.id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {subcategory.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>

                {/* Brand Field */}
                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={availableBrands.length === 0}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                availableBrands.length === 0
                                  ? "Select subcategory first"
                                  : "Select brand..."
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No brand</SelectItem>
                          {availableBrands.map((brand) => (
                            <SelectItem key={brand} value={brand}>
                              {brand}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="origin_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Origin Location</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="UK">UK</SelectItem>
                          <SelectItem value="Ghana">Ghana</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => {
                    const [inputValue, setInputValue] = React.useState(
                      field.value?.join(", ") || "",
                    );

                    // Sync inputValue with field value when form is reset
                    React.useEffect(() => {
                      setInputValue(field.value?.join(", ") || "");
                    }, [field.value]);

                    const processTagsFromInput = (value: string) => {
                      const tags = value
                        .split(",")
                        .map((tag) => tag.trim())
                        .filter((tag) => tag.length > 0);
                      field.onChange(tags.length > 0 ? tags : []);
                    };

                    return (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter tags separated by commas (e.g., electronics, smartphone, apple)"
                            className="min-h-[80px]"
                            value={inputValue}
                            onChange={(e) => {
                              setInputValue(e.target.value);
                            }}
                            onBlur={() => {
                              processTagsFromInput(inputValue);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="admin_notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Internal notes for administrators..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Internal notes visible only to administrators
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <div className="space-y-6">
              {/* Return Policy */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Return Policy
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="refundable"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Is this product refundable?</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(value) =>
                              field.onChange(value === "true")
                            }
                            value={field.value ? "true" : "false"}
                            className="flex flex-col space-y-1"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="true" />
                              </FormControl>
                              <FormLabel className="font-normal">Yes</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="false" />
                              </FormControl>
                              <FormLabel className="font-normal">No</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="refund_policy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Refund Policy</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Refund policy details"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This field is automatically populated based on your
                          selection.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Product Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Images</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Primary Image */}
                    <div>
                      <FormLabel className="text-sm font-medium">
                        Thumbnail Image *
                      </FormLabel>
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handlePrimaryImageChange}
                        className="mb-2 mt-1"
                      />
                      <FormDescription>
                        This is the image that is displayed on the shop page /
                        results page (required)
                      </FormDescription>

                      {primaryImagePreview && (
                        <div className="mt-4 relative inline-block">
                          <img
                            src={primaryImagePreview}
                            alt="Primary image preview"
                            className="w-32 h-32 object-cover rounded-md border-2 border-blue-500"
                          />
                          <div className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                            Primary
                          </div>
                          <button
                            type="button"
                            onClick={removePrimaryImage}
                            className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 text-xs"
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Shared Images */}
                    <div>
                      <FormLabel className="text-sm font-medium">
                        Shared Images (Optional)
                      </FormLabel>
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleSharedImagesChange}
                        className="mb-2 mt-1"
                      />
                      <FormDescription>
                        Upload additional product images that will be displayed
                        across all product variants.
                      </FormDescription>

                      {sharedImagePreviews.length > 0 && (
                        <div className="grid grid-cols-3 gap-4 mt-4">
                          {sharedImagePreviews.map((url, imageIndex) => (
                            <div key={imageIndex} className="relative">
                              <img
                                src={url}
                                alt={`Shared image ${imageIndex + 1}`}
                                className="w-full h-32 object-cover rounded-md"
                              />
                              <button
                                type="button"
                                onClick={() => removeSharedImage(imageIndex)}
                                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Type-specific Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Product Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {productType === "external" ? (
                    <>
                      <FormField
                        control={form.control}
                        name="details.origin_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Origin Store Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Store name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="details.origin_url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Product URL</FormLabel>
                            <FormControl>
                              <Input placeholder="https://..." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="details.is_affiliate"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Is Affiliate Product</FormLabel>
                              <FormDescription>
                                Check this if the product is associated with an
                                affiliate program
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="details.discount_code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Discount Code (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="Discount code" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  ) : (
                    <>
                      <FormField
                        control={form.control}
                        name="details.weight_kg"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (kg) (Optional)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                {...field}
                                value={field.value || ""}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? Number(e.target.value)
                                      : undefined,
                                  )
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          Dimensions (cm) (Optional)
                        </label>
                        <div className="grid grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.length"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Length"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.width"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Width"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.height"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="Height"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Shipping Rates */}
          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Shipping Rates
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  {shippingRateFields.map((field, index) => {
                    const toCountry = form.watch(
                      `shipping_rates.${index}.to_country`,
                    );
                    const toZone = form.watch(
                      `shipping_rates.${index}.to_zone`,
                    );
                    const isDutyEditable =
                      (toCountry === "Ghana" || toCountry === "Nigeria") &&
                      (toZone === "Accra" || toZone === "Lagos");

                    return (
                      <div
                        key={field.id}
                        className="space-y-4 p-4 border rounded-lg"
                      >
                        <div className="grid grid-cols-2 gap-4">
                          {/* To Country (read-only) */}
                          <div>
                            <label className="text-sm font-medium">
                              To Country
                            </label>
                            <div className="mt-1 px-3 py-2 border rounded-md bg-muted text-sm">
                              {toCountry}
                            </div>
                          </div>

                          {/* To Zone (read-only) */}
                          <div>
                            <label className="text-sm font-medium">
                              To Zone
                            </label>
                            <div className="mt-1 px-3 py-2 border rounded-md bg-muted text-sm">
                              {toZone}
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`shipping_rates.${index}.base_rate`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Base Rate</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    placeholder="0.00"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : 0,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`shipping_rates.${index}.duty`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Duty</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    placeholder="0.00"
                                    disabled={!isDutyEditable}
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) => {
                                      const nextValue = e.target.value
                                        ? Number(e.target.value)
                                        : 0;
                                      field.onChange(nextValue);
                                      if (
                                        isDutyEditable &&
                                        (toCountry === "Ghana" ||
                                          toCountry === "Nigeria")
                                      ) {
                                        const rates =
                                          form.getValues("shipping_rates");
                                        rates.forEach(
                                          (rate: any, i: number) => {
                                            if (
                                              i !== index &&
                                              rate.to_country === toCountry
                                            ) {
                                              form.setValue(
                                                `shipping_rates.${i}.duty` as any,
                                                nextValue,
                                              );
                                            }
                                          },
                                        );
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`shipping_rates.${index}.estimated_delivery_days`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Estimated Delivery Days (Optional)
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="1"
                                    placeholder="1"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`shipping_rates.${index}.currency`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Currency</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="GBP">GBP (£)</SelectItem>
                                    <SelectItem value="GHS">GHS (₵)</SelectItem>
                                    <SelectItem value="USD">USD ($)</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-1 gap-4">
                          <FormField
                            control={form.control}
                            name={`shipping_rates.${index}.rate_per_kg`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Rate per KG (Optional)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    placeholder="0.00"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : undefined,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {index > 0 && (
                          <Button
                            type="button"
                            variant="destructive"
                            onClick={() => removeShippingRate(index)}
                          >
                            Remove Rate
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    appendShippingRate({
                      to_country: "UK",
                      to_zone: "UK",
                      base_rate: 0,
                      duty: 0,
                      currency: "GBP",
                    })
                  }
                >
                  Add Shipping Rate
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Product Variants */}
          {variantAttributes.length > 0 && (
            <div className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package2 className="h-5 w-5" />
                    Product Variants
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4">
                    {form
                      .watch("variants")
                      ?.variants?.map((_: any, variantIndex: number) => (
                        <div
                          key={variantIndex}
                          className="space-y-4 p-4 border rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">
                              {variantIndex === 0
                                ? "Main Product Info"
                                : `Variant ${variantIndex}`}
                            </h4>
                            {variantIndex !== 0 && (
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={() => removeVariant(variantIndex)}
                              >
                                Remove
                              </Button>
                            )}
                          </div>

                          {/* Variant Attributes */}
                          <div className="grid grid-cols-2 gap-4">
                            {variantAttributes.map((attr) => (
                              <FormField
                                key={attr.name}
                                control={form.control}
                                name={
                                  `variants.variants.${variantIndex}.option_values.${attr.name}` as any
                                }
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>{attr.name}</FormLabel>
                                    <FormControl>
                                      <div>
                                        {attr.options || attr.values ? (
                                          <div>
                                            <Select
                                              onValueChange={(value) => {
                                                field.onChange(value);
                                                // Clear custom value if switching away from "Custom"
                                                if (value !== "Custom") {
                                                  setCustomAttributeValue(
                                                    variantIndex,
                                                    attr.name,
                                                    "",
                                                  );
                                                }
                                              }}
                                              defaultValue={String(
                                                field.value || "",
                                              )}
                                            >
                                              <SelectTrigger>
                                                <SelectValue
                                                  placeholder={`Select ${attr.name}`}
                                                />
                                              </SelectTrigger>
                                              <SelectContent>
                                                {(
                                                  attr.options || attr.values
                                                ).map((option: any) => (
                                                  <SelectItem
                                                    key={option}
                                                    value={String(option)}
                                                  >
                                                    {String(option)}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>

                                            {/* Check if "Custom" is selected */}
                                            {(() => {
                                              const isCustomSelected =
                                                String(field.value) ===
                                                "Custom";
                                              const hasCustomOption = (
                                                attr.options || attr.values
                                              )?.some(
                                                (v: any) =>
                                                  String(v).toLowerCase() ===
                                                  "custom",
                                              );

                                              return (
                                                hasCustomOption &&
                                                isCustomSelected
                                              );
                                            })() && (
                                              <div className="mt-2">
                                                <p className="text-sm text-gray-600 mb-1">
                                                  Custom {attr.name}:
                                                </p>
                                                <Input
                                                  type="text"
                                                  placeholder={`Enter custom ${attr.name}`}
                                                  value={getCustomAttributeValue(
                                                    variantIndex,
                                                    attr.name,
                                                  )}
                                                  onChange={(e) => {
                                                    // Store custom value in separate state
                                                    setCustomAttributeValue(
                                                      variantIndex,
                                                      attr.name,
                                                      e.target.value,
                                                    );
                                                  }}
                                                />
                                              </div>
                                            )}
                                          </div>
                                        ) : (
                                          <Input
                                            type="text"
                                            placeholder={`Enter ${attr.name}`}
                                            value={String(field.value || "")}
                                            onChange={field.onChange}
                                          />
                                        )}
                                      </div>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            ))}
                          </div>

                          {/* Variant Pricing */}
                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.price` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Base Price</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="0.00"
                                      {...field}
                                      value={field.value || ""}
                                      onChange={(e) =>
                                        field.onChange(
                                          e.target.value
                                            ? Number(e.target.value)
                                            : 0,
                                        )
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.sale_price` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Sale Price</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="0.00"
                                      {...field}
                                      value={field.value || ""}
                                      onChange={(e) =>
                                        field.onChange(
                                          e.target.value
                                            ? Number(e.target.value)
                                            : 0,
                                        )
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.currency` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Currency</FormLabel>
                                  <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select currency" />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="GBP">
                                        GBP (£)
                                      </SelectItem>
                                      <SelectItem value="GHS">
                                        GHS (₵)
                                      </SelectItem>
                                      <SelectItem value="USD">
                                        USD ($)
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.discount_percent` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Discount Percentage</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="0"
                                      min="0"
                                      max="100"
                                      {...field}
                                      value={field.value || ""}
                                      onChange={(e) =>
                                        field.onChange(
                                          e.target.value
                                            ? Number(e.target.value)
                                            : undefined,
                                        )
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Variant Stock */}
                          <FormField
                            control={form.control}
                            name={
                              `variants.variants.${variantIndex}.stock` as any
                            }
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Stock Quantity</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    placeholder="0"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value
                                          ? Number(e.target.value)
                                          : 0,
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Variant Images */}
                          <div>
                            <FormLabel className="text-sm font-medium">
                              Variant Images
                            </FormLabel>
                            <Input
                              type="file"
                              accept="image/*"
                              multiple
                              onChange={(e) =>
                                handleVariantImagesChange(variantIndex, e)
                              }
                              className="mb-2 mt-1"
                            />
                            <FormDescription>
                              Upload images specific to this variant
                            </FormDescription>

                            {variantImagePreviews[variantIndex] &&
                              variantImagePreviews[variantIndex].length > 0 && (
                                <div className="grid grid-cols-3 gap-4 mt-4">
                                  {variantImagePreviews[variantIndex].map(
                                    (url, imageIndex) => (
                                      <div
                                        key={imageIndex}
                                        className="relative"
                                      >
                                        <img
                                          src={url}
                                          alt={`Variant ${variantIndex + 1} image ${imageIndex + 1}`}
                                          className="w-full h-32 object-cover rounded-md"
                                        />
                                        <button
                                          type="button"
                                          onClick={() =>
                                            removeVariantImage(
                                              variantIndex,
                                              imageIndex,
                                            )
                                          }
                                          className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ),
                                  )}
                                </div>
                              )}
                          </div>
                        </div>
                      ))}
                  </div>

                  <Button type="button" variant="outline" onClick={addVariant}>
                    Add Variant
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="mt-6 flex justify-end gap-4">
            <Button
              variant="outline"
              type="button"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Updating..." : "Update Product"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
