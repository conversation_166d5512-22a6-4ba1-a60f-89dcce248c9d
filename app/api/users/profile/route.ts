import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

/**
 * GET /api/users/profile
 * Returns the profile of the currently authenticated user from the mp_users table.
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: user, error: userError } = await supabase.auth.getUser();

    if (userError || !user?.user) {
      return NextResponse.json(
        { error: "No authenticated user found." },
        { status: 401 }
      );
    }

    const { data, error } = await supabase
      .from("mp_users")
      .select("*")
      .eq("uuid", user.user.id)
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error fetching user data.", details: error.message },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: "User data not found." },
        { status: 404 }
      );
    }

    return NextResponse.json({ data });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 }
    );
  }
}
