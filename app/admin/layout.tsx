"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import {
  Package,
  FileEdit,
  Users,
  UserCircle,
  PackagePlus,
  ChartSplineIcon,
  ChevronDown,
  ShoppingCart,
  LogOut,
  Plus,
  Edit3,
  Store,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { AuthService } from "@/services/auth.service";

const navItems = [
  {
    label: "Dashboard",
    href: "/admin/dashboard",
    icon: ChartSplineIcon,
    group: "General",
  },

  {
    label: "Create Product",
    href: "/admin/products/new",
    icon: Plus,
    group: "E-commerce",
  },
  {
    label: "Edit Product",
    href: "/admin/products/edit",
    icon: Edit3,
    group: "E-commerce",
  },
  {
    label: "Generate Parcel",
    href: "/admin/parcels",
    icon: Package,
    group: "Virtual Address",
  },
  {
    label: "Edit Parcel",
    href: "/admin/edit-parcel",
    icon: FileEdit,
    group: "Virtual Address",
  },
  {
    label: "Consolidate Parcels",
    href: "/admin/consolidate",
    icon: PackagePlus,
    group: "Virtual Address",
  },
  {
    label: "Search Users",
    href: "/admin/users",
    icon: Users,
    group: "Virtual Address",
  },
  {
    label: "Profile",
    href: "/admin/profile",
    icon: UserCircle,
    group: "Profile",
  },
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  const renderNavigationItem = (item: (typeof navItems)[0]) => {
    const isActive = pathname === item.href;
    const Icon = item.icon;

    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
          "hover:bg-accent hover:text-accent-foreground",
          isActive ? "bg-accent text-accent-foreground" : "text-foreground/60",
        )}
      >
        <Icon className="h-4 w-4 mr-2" />
        {item.label}
      </Link>
    );
  };

  const virtualAddressItems = navItems.filter(
    (item) => item.group === "Virtual Address",
  );
  const ecommerceItems = navItems.filter((item) => item.group === "E-commerce");
  const generalItems = navItems.filter((item) => item.group === "General");
  const profileItems = navItems.filter((item) => item.group === "Profile");

  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      {pathname !== "/admin" && (
        <nav className="border-b sticky top-0 z-50 bg-background">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              {/* Logo */}
              <div className="flex items-center">
                <Link
                  href="/"
                  className="text-xl font-bold tracking-tight hover:text-primary/90 transition-colors"
                >
                  MailPallet
                </Link>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden sm:flex sm:items-center sm:space-x-1">
                {generalItems.map((item) => renderNavigationItem(item))}
                {/* E-commerce Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                      "hover:bg-accent hover:text-accent-foreground",
                      ecommerceItems.some((item) => pathname === item.href)
                        ? "bg-accent text-accent-foreground"
                        : "text-foreground/60",
                    )}
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    E-commerce
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {ecommerceItems.map((item) => (
                      <DropdownMenuItem key={item.href} asChild>
                        <Link href={item.href} className="flex items-center">
                          <item.icon className="h-4 w-4 mr-2" />
                          {item.label}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Virtual Address Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                      "hover:bg-accent hover:text-accent-foreground",
                      virtualAddressItems.some((item) => pathname === item.href)
                        ? "bg-accent text-accent-foreground"
                        : "text-foreground/60",
                    )}
                  >
                    <Package className="h-4 w-4 mr-2" />
                    Virtual Address
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {virtualAddressItems.map((item) => (
                      <DropdownMenuItem key={item.href} asChild>
                        <Link href={item.href} className="flex items-center">
                          <item.icon className="h-4 w-4 mr-2" />
                          {item.label}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* User Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors",
                      "hover:bg-accent hover:text-accent-foreground text-foreground/60",
                    )}
                  >
                    <UserCircle className="h-4 w-4 mr-2" />
                    Account
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link
                        href="/home/<USER>"
                        className="flex items-center text-green-600 hover:bg-green-50"
                      >
                        <Store className="h-4 w-4 mr-2 text-green-600" />
                        Shop
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/admin/profile" className="flex items-center">
                        <UserCircle className="h-4 w-4 mr-2" />
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={async () => {
                        const authService = new AuthService();
                        await authService.signOut();
                      }}
                      className="flex items-center text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Mobile Menu Button */}
              <div className="sm:hidden">
                <button
                  type="button"
                  className="inline-flex items-center justify-center rounded-md p-2 hover:bg-accent hover:text-accent-foreground"
                  aria-controls="mobile-menu"
                  aria-expanded="false"
                >
                  <span className="sr-only">Open main menu</span>
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Menu */}
          <div className="sm:hidden" id="mobile-menu">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {generalItems.map((item) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors",
                      "hover:bg-accent hover:text-accent-foreground",
                      isActive
                        ? "bg-accent text-accent-foreground"
                        : "text-foreground/60",
                    )}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.label}
                  </Link>
                );
              })}

              {ecommerceItems.map((item) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors",
                      "hover:bg-accent hover:text-accent-foreground",
                      isActive
                        ? "bg-accent text-accent-foreground"
                        : "text-foreground/60",
                    )}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.label}
                  </Link>
                );
              })}

              {virtualAddressItems.map((item) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors",
                      "hover:bg-accent hover:text-accent-foreground",
                      isActive
                        ? "bg-accent text-accent-foreground"
                        : "text-foreground/60",
                    )}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.label}
                  </Link>
                );
              })}

              <div className="border-t mt-2 pt-2">
                <Link
                  href="/home/<USER>"
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors",
                    "hover:bg-green-50",
                    pathname === "/home/<USER>"
                      ? "bg-accent text-accent-foreground"
                      : "text-green-600",
                  )}
                >
                  <Store className="h-5 w-5 mr-3 text-green-600" />
                  Shop
                </Link>

                <Link
                  href="/admin/profile"
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    pathname === "/admin/profile"
                      ? "bg-accent text-accent-foreground"
                      : "text-foreground/60",
                  )}
                >
                  <UserCircle className="h-5 w-5 mr-3" />
                  Profile
                </Link>

                <button
                  onClick={async () => {
                    const authService = new AuthService();
                    await authService.signOut();
                  }}
                  className="w-full text-left flex items-center px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <LogOut className="h-5 w-5 mr-3" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </nav>
      )}

      {/* Main Content */}
      <main className="flex-1 bg-accent/5">
        <div className="max-w-7xl mx-auto">{children}</div>
      </main>

      {/* Footer */}
      <footer className="border-t py-4 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-sm text-muted-foreground text-center">
            © {new Date().getFullYear()} MailPallet. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
