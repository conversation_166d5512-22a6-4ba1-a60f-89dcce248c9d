import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { Order } from "@/data/models/order.model";
import {
  getAuthenticatedUser,
  canAccessUserDataById,
  createUnauthorizedResponse,
  createForbiddenResponse,
} from "@/lib/auth-helpers";

export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const { user, error: authError } = await getAuthenticatedUser();
    if (authError || !user) {
      return createUnauthorizedResponse("Authentication required");
    }

    const supabase = await createClient();
    const body = await request.json();

    // Validate required fields
    if (!body || typeof body !== "object") {
      return NextResponse.json(
        { error: "Order data is required." },
        { status: 400 },
      );
    }

    // Ensure user_id is provided and validate ownership
    if (!body.user_id) {
      return NextResponse.json(
        { error: "user_id is required in order data." },
        { status: 400 },
      );
    }

    // Check if the authenticated user can create orders for the specified user_id
    const { canAccess, error: accessError } = await canAccessUserDataById(
      body.user_id.toString(),
    );

    if (!canAccess) {
      return createForbiddenResponse(
        accessError || "Cannot create orders for other users",
      );
    }

    // Insert the new order
    const { data, error } = await supabase
      .from("mp_ecommerce_orders")
      .insert([body])
      .select("*")
      .single();

    if (error) {
      return NextResponse.json(
        { error: "Error creating order.", details: error.message },
        { status: 500 },
      );
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (err: any) {
    return NextResponse.json(
      { error: "Server error.", details: err.message },
      { status: 500 },
    );
  }
}
