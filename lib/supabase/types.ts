// IMPORTANT! Regenerate types from supabase before use!
// Either with the CLI or from the project dashboard
// https://supabase.com/docs/guides/api/rest/generating-types

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)";
  };
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      consolidation_parcel_requests: {
        Row: {
          code: string;
          created_at: string | null;
          id: number;
          parcel_id: number | null;
          status: number | null;
          updated_at: string | null;
          user_id: number;
          uuid: string | null;
        };
        Insert: {
          code: string;
          created_at?: string | null;
          id?: number;
          parcel_id?: number | null;
          status?: number | null;
          updated_at?: string | null;
          user_id: number;
          uuid?: string | null;
        };
        Update: {
          code?: string;
          created_at?: string | null;
          id?: number;
          parcel_id?: number | null;
          status?: number | null;
          updated_at?: string | null;
          user_id?: number;
          uuid?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "consolidation_parcel_requests_parcel_id_fkey";
            columns: ["parcel_id"];
            isOneToOne: false;
            referencedRelation: "users_parcels_details";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "consolidation_parcel_requests_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "mp_users";
            referencedColumns: ["id"];
          },
        ];
      };
      mailpallet_locations: {
        Row: {
          address_line_one: string;
          address_line_two: string | null;
          city: string | null;
          country: string | null;
          id: number;
          phone_number: string | null;
          postcode: string | null;
          state: string | null;
          zip_code: string | null;
        };
        Insert: {
          address_line_one: string;
          address_line_two?: string | null;
          city?: string | null;
          country?: string | null;
          id?: number;
          phone_number?: string | null;
          postcode?: string | null;
          state?: string | null;
          zip_code?: string | null;
        };
        Update: {
          address_line_one?: string;
          address_line_two?: string | null;
          city?: string | null;
          country?: string | null;
          id?: number;
          phone_number?: string | null;
          postcode?: string | null;
          state?: string | null;
          zip_code?: string | null;
        };
        Relationships: [];
      };
      mp_additional_shipping_fees: {
        Row: {
          applies_to_method_id: number | null;
          fee_amount: number | null;
          fee_name: string | null;
          id: number | null;
          is_percentage: string | null;
        };
        Insert: {
          applies_to_method_id?: number | null;
          fee_amount?: number | null;
          fee_name?: string | null;
          id?: number | null;
          is_percentage?: string | null;
        };
        Update: {
          applies_to_method_id?: number | null;
          fee_amount?: number | null;
          fee_name?: string | null;
          id?: number | null;
          is_percentage?: string | null;
        };
        Relationships: [];
      };
      mp_admins: {
        Row: {
          created_at: string;
          email: string;
          first_name: string;
          id: string;
          last_name: string;
          password: string;
          permissions: Json;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string;
          email: string;
          first_name: string;
          id?: string;
          last_name: string;
          password: string;
          permissions: Json;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string;
          email?: string;
          first_name?: string;
          id?: string;
          last_name?: string;
          password?: string;
          permissions?: Json;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      mp_awaiting_fulfilment: {
        Row: {
          tracking_id: string;
          uuid: string;
        };
        Insert: {
          tracking_id?: string;
          uuid?: string;
        };
        Update: {
          tracking_id?: string;
          uuid?: string;
        };
        Relationships: [];
      };
      mp_ecommerce_categories: {
        Row: {
          brands: Json | null;
          created_at: string | null;
          description: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          parent_id: string | null;
          slug: string;
          updated_at: string | null;
          variant: Json | null;
        };
        Insert: {
          brands?: Json | null;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          parent_id?: string | null;
          slug: string;
          updated_at?: string | null;
          variant?: Json | null;
        };
        Update: {
          brands?: Json | null;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          parent_id?: string | null;
          slug?: string;
          updated_at?: string | null;
          variant?: Json | null;
        };
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey";
            columns: ["parent_id"];
            isOneToOne: false;
            referencedRelation: "mp_ecommerce_categories";
            referencedColumns: ["id"];
          },
        ];
      };
      mp_ecommerce_exchange_rates: {
        Row: {
          country_name: string;
          created_at: string | null;
          currency_code: string;
          id: string;
          last_updated: string | null;
          rate_per_gbp: number;
          updated_at: string | null;
        };
        Insert: {
          country_name: string;
          created_at?: string | null;
          currency_code: string;
          id?: string;
          last_updated?: string | null;
          rate_per_gbp: number;
          updated_at?: string | null;
        };
        Update: {
          country_name?: string;
          created_at?: string | null;
          currency_code?: string;
          id?: string;
          last_updated?: string | null;
          rate_per_gbp?: number;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      mp_ecommerce_orders: {
        Row: {
          admin_notes: string | null;
          cancelled_at: string | null;
          confirmed_at: string | null;
          created_at: string;
          currency: Database["public"]["Enums"]["currency_type"];
          customer_notes: string | null;
          delivered_at: string | null;
          discount_amount: number;
          id: string;
          is_canceled: boolean | null;
          items: Json;
          marketing_opt_in: boolean;
          order_number: string;
          payment_details: Json;
          shipped_at: string | null;
          shipping_address: Json;
          shipping_cost: number;
          shipping_details: Json | null;
          source: Database["public"]["Enums"]["order_source_type"];
          status: Database["public"]["Enums"]["order_status_type"];
          status_history: Json;
          subtotal: number;
          total_amount: number;
          updated_at: string;
          user_id: number | null;
        };
        Insert: {
          admin_notes?: string | null;
          cancelled_at?: string | null;
          confirmed_at?: string | null;
          created_at?: string;
          currency: Database["public"]["Enums"]["currency_type"];
          customer_notes?: string | null;
          delivered_at?: string | null;
          discount_amount?: number;
          id?: string;
          is_canceled?: boolean | null;
          items?: Json;
          marketing_opt_in?: boolean;
          order_number: string;
          payment_details: Json;
          shipped_at?: string | null;
          shipping_address: Json;
          shipping_cost?: number;
          shipping_details?: Json | null;
          source?: Database["public"]["Enums"]["order_source_type"];
          status?: Database["public"]["Enums"]["order_status_type"];
          status_history?: Json;
          subtotal: number;
          total_amount: number;
          updated_at?: string;
          user_id?: number | null;
        };
        Update: {
          admin_notes?: string | null;
          cancelled_at?: string | null;
          confirmed_at?: string | null;
          created_at?: string;
          currency?: Database["public"]["Enums"]["currency_type"];
          customer_notes?: string | null;
          delivered_at?: string | null;
          discount_amount?: number;
          id?: string;
          is_canceled?: boolean | null;
          items?: Json;
          marketing_opt_in?: boolean;
          order_number?: string;
          payment_details?: Json;
          shipped_at?: string | null;
          shipping_address?: Json;
          shipping_cost?: number;
          shipping_details?: Json | null;
          source?: Database["public"]["Enums"]["order_source_type"];
          status?: Database["public"]["Enums"]["order_status_type"];
          status_history?: Json;
          subtotal?: number;
          total_amount?: number;
          updated_at?: string;
          user_id?: number | null;
        };
        Relationships: [];
      };
      mp_ecommerce_products: {
        Row: {
          admin_notes: string | null;
          archived: boolean;
          brand: string | null;
          category: string | null;
          condition: Database["public"]["Enums"]["product_condition"] | null;
          created_at: string | null;
          currency: string | null;
          description: string | null;
          details: Json | null;
          id: string;
          origin_location:
            | Database["public"]["Enums"]["product_origin_location"]
            | null;
          primary_data: Json | null;
          primary_image: Json | null;
          refund_policy: string;
          refundable: boolean;
          shared_images: Json | null;
          shipping_rates: Json | null;
          subcategory: string | null;
          tags: Json | null;
          title: string | null;
          type: Database["public"]["Enums"]["product_type"] | null;
          updated_at: string | null;
          variants: Json | null;
        };
        Insert: {
          admin_notes?: string | null;
          archived?: boolean;
          brand?: string | null;
          category?: string | null;
          condition?: Database["public"]["Enums"]["product_condition"] | null;
          created_at?: string | null;
          currency?: string | null;
          description?: string | null;
          details?: Json | null;
          id?: string;
          origin_location?:
            | Database["public"]["Enums"]["product_origin_location"]
            | null;
          primary_data?: Json | null;
          primary_image?: Json | null;
          refund_policy?: string;
          refundable?: boolean;
          shared_images?: Json | null;
          shipping_rates?: Json | null;
          subcategory?: string | null;
          tags?: Json | null;
          title?: string | null;
          type?: Database["public"]["Enums"]["product_type"] | null;
          updated_at?: string | null;
          variants?: Json | null;
        };
        Update: {
          admin_notes?: string | null;
          archived?: boolean;
          brand?: string | null;
          category?: string | null;
          condition?: Database["public"]["Enums"]["product_condition"] | null;
          created_at?: string | null;
          currency?: string | null;
          description?: string | null;
          details?: Json | null;
          id?: string;
          origin_location?:
            | Database["public"]["Enums"]["product_origin_location"]
            | null;
          primary_data?: Json | null;
          primary_image?: Json | null;
          refund_policy?: string;
          refundable?: boolean;
          shared_images?: Json | null;
          shipping_rates?: Json | null;
          subcategory?: string | null;
          tags?: Json | null;
          title?: string | null;
          type?: Database["public"]["Enums"]["product_type"] | null;
          updated_at?: string | null;
          variants?: Json | null;
        };
        Relationships: [];
      };
      mp_shipping_methods: {
        Row: {
          dim_divisor: number | null;
          handling_fee: number | null;
          id: number;
          method_name: string;
          min_weight: number | null;
          origin_country: string;
        };
        Insert: {
          dim_divisor?: number | null;
          handling_fee?: number | null;
          id?: number;
          method_name: string;
          min_weight?: number | null;
          origin_country: string;
        };
        Update: {
          dim_divisor?: number | null;
          handling_fee?: number | null;
          id?: number;
          method_name?: string;
          min_weight?: number | null;
          origin_country?: string;
        };
        Relationships: [];
      };
      mp_shipping_routes: {
        Row: {
          base_price: number;
          destination_country: string;
          id: number;
          is_active: boolean;
          origin_country: string;
          shipping_method_id: number;
        };
        Insert: {
          base_price: number;
          destination_country: string;
          id: number;
          is_active: boolean;
          origin_country: string;
          shipping_method_id: number;
        };
        Update: {
          base_price?: number;
          destination_country?: string;
          id?: number;
          is_active?: boolean;
          origin_country?: string;
          shipping_method_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "mp_shipping_routes_method_id_fkey";
            columns: ["shipping_method_id"];
            isOneToOne: false;
            referencedRelation: "mp_shipping_methods";
            referencedColumns: ["id"];
          },
        ];
      };
      mp_users: {
        Row: {
          country_code: string | null;
          email: string;
          first_name: string | null;
          id: number;
          last_name: string | null;
          phone_number: string | null;
          receive_marketing: boolean | null;
          signed_privacy_policy: boolean | null;
          signed_shipping_policy: boolean | null;
          uuid: string | null;
        };
        Insert: {
          country_code?: string | null;
          email: string;
          first_name?: string | null;
          id?: number;
          last_name?: string | null;
          phone_number?: string | null;
          receive_marketing?: boolean | null;
          signed_privacy_policy?: boolean | null;
          signed_shipping_policy?: boolean | null;
          uuid?: string | null;
        };
        Update: {
          country_code?: string | null;
          email?: string;
          first_name?: string | null;
          id?: number;
          last_name?: string | null;
          phone_number?: string | null;
          receive_marketing?: boolean | null;
          signed_privacy_policy?: boolean | null;
          signed_shipping_policy?: boolean | null;
          uuid?: string | null;
        };
        Relationships: [];
      };
      users_address: {
        Row: {
          Address: string;
          City: string;
          Company: string | null;
          DeliverTo: string;
          EmailAddress: string;
          FirstName: string;
          LastName: string;
          OutsideAccra: boolean | null;
          OutsideLagos: boolean | null;
          PostalCode: string | null;
          StateProvince: string;
          user_id: number;
          uuid: string | null;
          WithinAccra: boolean | null;
          WithinLagos: boolean | null;
        };
        Insert: {
          Address: string;
          City: string;
          Company?: string | null;
          DeliverTo: string;
          EmailAddress: string;
          FirstName: string;
          LastName: string;
          OutsideAccra?: boolean | null;
          OutsideLagos?: boolean | null;
          PostalCode?: string | null;
          StateProvince: string;
          user_id?: number;
          uuid?: string | null;
          WithinAccra?: boolean | null;
          WithinLagos?: boolean | null;
        };
        Update: {
          Address?: string;
          City?: string;
          Company?: string | null;
          DeliverTo?: string;
          EmailAddress?: string;
          FirstName?: string;
          LastName?: string;
          OutsideAccra?: boolean | null;
          OutsideLagos?: boolean | null;
          PostalCode?: string | null;
          StateProvince?: string;
          user_id?: number;
          uuid?: string | null;
          WithinAccra?: boolean | null;
          WithinLagos?: boolean | null;
        };
        Relationships: [];
      };
      users_parcels_details: {
        Row: {
          bought_from: string | null;
          country: string | null;
          created_at: string | null;
          dangerous_goods_price: number | null;
          delivered_date: string | null;
          dimensions: string | null;
          discount_amount: number | null;
          duty: number | null;
          est_due_date: string | null;
          id: number;
          invoice: string | null;
          is_consolidated: boolean | null;
          is_dangerous_goods: boolean | null;
          is_deleted: boolean | null;
          is_discounted: boolean | null;
          is_insured: boolean | null;
          is_payed_for: boolean | null;
          items_pdf: Json | null;
          items_value: number | null;
          package_content: string | null;
          package_weight: number | null;
          payment_date: string | null;
          payment_method: string | null;
          received_at: string | null;
          received_on: string | null;
          selected_shipping_method_id: number | null;
          shipping_address: string | null;
          shipping_options: Json | null;
          status: string | null;
          storage_fee: number | null;
          third_party_tracking: string | null;
          total_due: number | null;
          tracking_id: string;
          updated_at: string | null;
          user_id: number | null;
          uuid: string | null;
          vendor_tracking: string | null;
        };
        Insert: {
          bought_from?: string | null;
          country?: string | null;
          created_at?: string | null;
          dangerous_goods_price?: number | null;
          delivered_date?: string | null;
          dimensions?: string | null;
          discount_amount?: number | null;
          duty?: number | null;
          est_due_date?: string | null;
          id?: number;
          invoice?: string | null;
          is_consolidated?: boolean | null;
          is_dangerous_goods?: boolean | null;
          is_deleted?: boolean | null;
          is_discounted?: boolean | null;
          is_insured?: boolean | null;
          is_payed_for?: boolean | null;
          items_pdf?: Json | null;
          items_value?: number | null;
          package_content?: string | null;
          package_weight?: number | null;
          payment_date?: string | null;
          payment_method?: string | null;
          received_at?: string | null;
          received_on?: string | null;
          selected_shipping_method_id?: number | null;
          shipping_address?: string | null;
          shipping_options?: Json | null;
          status?: string | null;
          storage_fee?: number | null;
          third_party_tracking?: string | null;
          total_due?: number | null;
          tracking_id: string;
          updated_at?: string | null;
          user_id?: number | null;
          uuid?: string | null;
          vendor_tracking?: string | null;
        };
        Update: {
          bought_from?: string | null;
          country?: string | null;
          created_at?: string | null;
          dangerous_goods_price?: number | null;
          delivered_date?: string | null;
          dimensions?: string | null;
          discount_amount?: number | null;
          duty?: number | null;
          est_due_date?: string | null;
          id?: number;
          invoice?: string | null;
          is_consolidated?: boolean | null;
          is_dangerous_goods?: boolean | null;
          is_deleted?: boolean | null;
          is_discounted?: boolean | null;
          is_insured?: boolean | null;
          is_payed_for?: boolean | null;
          items_pdf?: Json | null;
          items_value?: number | null;
          package_content?: string | null;
          package_weight?: number | null;
          payment_date?: string | null;
          payment_method?: string | null;
          received_at?: string | null;
          received_on?: string | null;
          selected_shipping_method_id?: number | null;
          shipping_address?: string | null;
          shipping_options?: Json | null;
          status?: string | null;
          storage_fee?: number | null;
          third_party_tracking?: string | null;
          total_due?: number | null;
          tracking_id?: string;
          updated_at?: string | null;
          user_id?: number | null;
          uuid?: string | null;
          vendor_tracking?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "users_parcels_details_selected_shipping_method_id_fkey";
            columns: ["selected_shipping_method_id"];
            isOneToOne: false;
            referencedRelation: "mp_shipping_methods";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      count_parcels_per_country: {
        Args: Record<PropertyKey, never>;
        Returns: {
          country: string;
          parcel_count: number;
        }[];
      };
      get_category_path: {
        Args: { category_id: string };
        Returns: string;
      };
      get_subcategories: {
        Args: { parent_category_id: string };
        Returns: {
          id: string;
          name: string;
          slug: string;
          level: number;
        }[];
      };
    };
    Enums: {
      currency_type: "GBP" | "GHS" | "NGN" | "USD";
      location_type: "UK" | "Ghana";
      order_source_type: "web" | "mobile_app" | "admin" | "api";
      order_status_type:
        | "pending"
        | "confirmed"
        | "processing"
        | "shipped"
        | "delivered"
        | "cancelled";
      payment_method_type:
        | "card"
        | "bank_transfer"
        | "paypal"
        | "apple_pay"
        | "google_pay";
      payment_status_type: "pending" | "completed" | "failed" | "refunded";
      product_condition: "New" | "Used";
      product_condition_type: "New" | "Used";
      product_origin_location: "UK" | "Ghana";
      product_type: "external" | "internal";
      shipping_method_type:
        | "standard"
        | "express"
        | "overnight"
        | "international";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  "public"
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      currency_type: ["GBP", "GHS", "NGN", "USD"],
      location_type: ["UK", "Ghana"],
      order_source_type: ["web", "mobile_app", "admin", "api"],
      order_status_type: [
        "pending",
        "confirmed",
        "processing",
        "shipped",
        "delivered",
        "cancelled",
      ],
      payment_method_type: [
        "card",
        "bank_transfer",
        "paypal",
        "apple_pay",
        "google_pay",
      ],
      payment_status_type: ["pending", "completed", "failed", "refunded"],
      product_condition: ["New", "Used"],
      product_condition_type: ["New", "Used"],
      product_origin_location: ["UK", "Ghana"],
      product_type: ["external", "internal"],
      shipping_method_type: [
        "standard",
        "express",
        "overnight",
        "international",
      ],
    },
  },
} as const;
