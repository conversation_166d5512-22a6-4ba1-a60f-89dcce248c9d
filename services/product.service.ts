import { supabase } from "@/lib/supabase_client";
import { Product, ProductVariant, ProductCreationData, ProductUpdateData, ProductVariantItem } from "@/data/models/product.model";

export class ProductService {
  async createProduct(
    creationData: ProductCreationData,
  ): Promise<{ product: Product }> {
    try {
      const {
        product,
        primaryImage,
        shared_images = [],
        variant_images = {},
      } = creationData;

      // 1. Upload primary image to Supabase Storage
      const primaryFileName = `${Date.now()}-${primaryImage.name}`;
      const { data: primaryData, error: primaryError } = await supabase.storage
        .from("product-images")
        .upload(primaryFileName, primaryImage);

      if (primaryError) throw primaryError;

      // Get the public URL for the primary image
      const {
        data: { publicUrl: primaryPublicUrl },
      } = supabase.storage
        .from("product-images")
        .getPublicUrl(primaryData.path);

      const primaryImageObject = {
        url: primaryPublicUrl,
        name: primaryFileName,
      };

      // 2. Upload shared images to Supabase Storage
      const sharedImageObjects: Array<{ url: string; name: string }> = [];
      if (shared_images.length > 0) {
        for (const sharedImage of shared_images) {
          const sharedFileName = `${Date.now()}-${Math.random()}-${sharedImage.name}`;
          const { data: sharedData, error: sharedError } =
            await supabase.storage
              .from("product-images")
              .upload(sharedFileName, sharedImage);

          if (sharedError) {
            // Clean up any already uploaded images on error
            const imagesToCleanup = [
              primaryFileName,
              ...sharedImageObjects.map((img) => img.name),
            ];
            await supabase.storage
              .from("product-images")
              .remove(imagesToCleanup);
            throw sharedError;
          }

          const {
            data: { publicUrl: sharedPublicUrl },
          } = supabase.storage
            .from("product-images")
            .getPublicUrl(sharedData.path);

          sharedImageObjects.push({
            url: sharedPublicUrl,
            name: sharedFileName,
          });
        }
      }

      // 3. Process variant images and update variants with image URLs
      let processedProduct = { ...product };
      if (processedProduct.variants && Object.keys(variant_images).length > 0) {
        const updatedVariants = [...(processedProduct.variants.variants || [])];

        for (const [variantIndexStr, images] of Object.entries(
          variant_images,
        )) {
          const variantIndex = parseInt(variantIndexStr);
          if (variantIndex < updatedVariants.length && images.length > 0) {
            const variantImageObjects: Array<{ url: string; name: string }> =
              [];

            for (const variantImage of images) {
              const variantFileName = `variant-${Date.now()}-${Math.random()}-${variantImage.name}`;
              const { data: variantData, error: variantError } =
                await supabase.storage
                  .from("product-images")
                  .upload(variantFileName, variantImage);

              if (variantError) {
                // Clean up any already uploaded images on error
                const imagesToCleanup = [
                  primaryFileName,
                  ...sharedImageObjects.map((img) => img.name),
                  ...variantImageObjects.map((img) => img.name),
                ];
                await supabase.storage
                  .from("product-images")
                  .remove(imagesToCleanup);
                throw variantError;
              }

              const {
                data: { publicUrl: variantPublicUrl },
              } = supabase.storage
                .from("product-images")
                .getPublicUrl(variantData.path);

              variantImageObjects.push({
                url: variantPublicUrl,
                name: variantFileName,
              });
            }

            // Add images to the variant
            updatedVariants[variantIndex] = {
              ...updatedVariants[variantIndex],
              images: variantImageObjects,
            };
          }
        }

        processedProduct.variants = {
          ...processedProduct.variants,
          variants: updatedVariants,
        };
      }

      // 4. Create product record in database
      // Use API endpoint to create product (images already uploaded)
      const response = await fetch("/api/products", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...processedProduct,
          primary_image: primaryImageObject,
          shared_images: sharedImageObjects,
          archived: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }),
      });
      const result = await response.json();
      if (!response.ok) {
        // If product creation fails, clean up uploaded images
        const variantImageNames =
          processedProduct.variants?.variants?.flatMap(
            (variant) => variant.images?.map((img: any) => img.name) || [],
          ) || [];
        const imagesToCleanup = [
          primaryFileName,
          ...sharedImageObjects.map((img) => img.name),
          ...variantImageNames,
        ];
        await supabase.storage.from("product-images").remove(imagesToCleanup);
        throw new Error(result.error || "Error creating product");
      }
      return { product: result.data };
    } catch (error) {
      console.error("Error creating product:", error);
      throw error;
    }
  }

  async updateProduct(
    id: string,
    updateData: ProductUpdateData,
  ): Promise<{ product: Product }> {
    try {
      const {
        productUpdates = {},
        primaryImage,
        shared_images = [],
        variant_images = {},
        removed_shared_image_names = [],
        removed_variant_image_names = [],
      } = updateData;

      // Get existing product to compare images
      // Use API endpoint to get product by id
      const getResponse = await fetch(
        `/api/products?id=${encodeURIComponent(id)}`,
      );
      const getResult = await getResponse.json();
      const existingProduct = getResult.data;
      if (!getResponse.ok || !existingProduct) {
        throw new Error(getResult.error || "Product not found");
      }

      let updatedProductData = { ...productUpdates };
      const newlyUploadedImages: Array<{ url: string; name: string }> = [];
      const namesMarkedForDeletion: string[] = [];

      // 1. Handle primary image update if provided
      if (primaryImage) {
        const primaryFileName = `${Date.now()}-${primaryImage.name}`;
        const { data: primaryData, error: primaryError } =
          await supabase.storage
            .from("product-images")
            .upload(primaryFileName, primaryImage);

        if (primaryError) throw primaryError;

        const {
          data: { publicUrl: primaryPublicUrl },
        } = supabase.storage
          .from("product-images")
          .getPublicUrl(primaryData.path);

        const newPrimaryImage = {
          url: primaryPublicUrl,
          name: primaryFileName,
        };

        updatedProductData.primary_image = newPrimaryImage;
        newlyUploadedImages.push(newPrimaryImage);

        // Delete old primary image if it exists
        if (existingProduct.primary_image?.name) {
          await supabase.storage
            .from("product-images")
            .remove([existingProduct.primary_image.name]);
        }
      }

      // 2. Handle shared images: start from existing, add new, then remove marked
      if (Array.isArray(existingProduct.shared_images)) {
        updatedProductData.shared_images = [...existingProduct.shared_images];
      }

      if (shared_images.length > 0) {
        const newSharedImageObjects: Array<{ url: string; name: string }> = [];
        for (const sharedImage of shared_images) {
          const sharedFileName = `${Date.now()}-${Math.random()}-${sharedImage.name}`;
          const { data: sharedData, error: sharedError } =
            await supabase.storage
              .from("product-images")
              .upload(sharedFileName, sharedImage);

          if (sharedError) {
            // Clean up any already uploaded images on error
            const imagesToCleanup = [
              ...newlyUploadedImages.map((img) => img.name),
              ...newSharedImageObjects.map((img) => img.name),
            ];
            await supabase.storage
              .from("product-images")
              .remove(imagesToCleanup);
            throw sharedError;
          }

          const {
            data: { publicUrl: sharedPublicUrl },
          } = supabase.storage
            .from("product-images")
            .getPublicUrl(sharedData.path);

          newSharedImageObjects.push({ url: sharedPublicUrl, name: sharedFileName });
        }

        // Combine existing shared images with new ones
        const existingSharedImages = existingProduct.shared_images || [];
        updatedProductData.shared_images = [
          ...existingSharedImages,
          ...newSharedImageObjects,
        ];
        newlyUploadedImages.push(...newSharedImageObjects);
        const baseShared = Array.isArray(updatedProductData.shared_images) ? updatedProductData.shared_images : (existingProduct.shared_images || []);
        updatedProductData.shared_images = [...baseShared, ...newSharedImageObjects];
      }

      if (removed_shared_image_names && removed_shared_image_names.length > 0) {
        const baseShared = Array.isArray(updatedProductData.shared_images) ? updatedProductData.shared_images : (existingProduct.shared_images || []);
        updatedProductData.shared_images = baseShared.filter((img: { url: string; name: string }) => !removed_shared_image_names.includes(img.name));
        namesMarkedForDeletion.push(...removed_shared_image_names);
      }

      // 3. Process variant images and update variants with image URLs
      if (
        Object.keys(variant_images).length > 0 &&
        updatedProductData.variants
      ) {
        const updatedVariants = [
          ...(updatedProductData.variants.variants || []),
        ];

        for (const [variantIndexStr, images] of Object.entries(
          variant_images,
        )) {
          const variantIndex = parseInt(variantIndexStr);
          if (variantIndex < updatedVariants.length && images.length > 0) {
            const variantImageObjects: Array<{ url: string; name: string }> =
              [];

            for (const variantImage of images) {
              const variantFileName = `variant-${Date.now()}-${Math.random()}-${variantImage.name}`;
              const { data: variantData, error: variantError } =
                await supabase.storage
                  .from("product-images")
                  .upload(variantFileName, variantImage);

              if (variantError) {
                // Clean up any already uploaded images on error
                const imagesToCleanup = [
                  ...newlyUploadedImages.map((img) => img.name),
                  ...variantImageObjects.map((img) => img.name),
                ];
                await supabase.storage
                  .from("product-images")
                  .remove(imagesToCleanup);
                throw variantError;
              }

              const {
                data: { publicUrl: variantPublicUrl },
              } = supabase.storage
                .from("product-images")
                .getPublicUrl(variantData.path);

              variantImageObjects.push({
                url: variantPublicUrl,
                name: variantFileName,
              });
            }

            // Combine existing variant images with new ones
            const existingVariantImages =
              updatedVariants[variantIndex].images || [];
            updatedVariants[variantIndex] = {
              ...updatedVariants[variantIndex],
              images: [...existingVariantImages, ...variantImageObjects],
            };

            newlyUploadedImages.push(...variantImageObjects);
          }
        }

        updatedProductData.variants = {
          ...updatedProductData.variants,
          variants: updatedVariants,
        };
      }

      // Apply deletions to variant images regardless of uploads, if requested
      if (removed_variant_image_names && removed_variant_image_names.length > 0) {
        const baseVariants = (updatedProductData.variants?.variants && updatedProductData.variants.variants.length > 0)
          ? updatedProductData.variants.variants
          : (existingProduct.variants?.variants || []);

        if (baseVariants.length > 0) {
          const filteredVariants = baseVariants.map((variant: ProductVariantItem) => {
            const existingImages: Array<{ url: string; name: string }> = variant.images || [];
            const keptImages = existingImages.filter((img: { url: string; name: string }) => !removed_variant_image_names.includes(img.name));
            return { ...variant, images: keptImages };
          });

          const baseMasterOptions = updatedProductData.variants?.master_options || existingProduct.variants?.master_options || [];
          updatedProductData.variants = {
            variants: filteredVariants,
            master_options: baseMasterOptions,
          };
        }

        namesMarkedForDeletion.push(...removed_variant_image_names);
      }

      // 4. Update product record if there are product updates
      let updatedProduct = existingProduct;
      if (Object.keys(updatedProductData).length > 0) {
        updatedProductData.updated_at = new Date();

        const response = await fetch("/api/products", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ id, ...updatedProductData }),
        });
        const result = await response.json();

        if (!response.ok) {
          // Clean up newly uploaded images on failure
          if (newlyUploadedImages.length > 0) {
            await Promise.all(
              newlyUploadedImages.map(async (image) => {
                await supabase.storage
                  .from("product-images")
                  .remove([image.name]);
              }),
            );
          }
          throw new Error(result.error || "Error updating product");
        }
        updatedProduct = result.data;
      }

      // 5. Remove any images that were marked for deletion from storage (best-effort)
      if (namesMarkedForDeletion.length > 0) {
        try {
          await supabase.storage
            .from("product-images")
            .remove(namesMarkedForDeletion);
        } catch (e) {
          console.warn("Failed to delete some images from storage:", namesMarkedForDeletion, e);
        }
      }
      return { product: updatedProduct };
    } catch (error) {
      console.error("Error updating product:", error);
      throw error;
    }
  }

  async archiveProduct(id: string): Promise<void> {
    try {
      const response = await fetch("/api/products/archive", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id }),
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Error archiving product");
      }
    } catch (error) {
      console.error("Error archiving product:", error);
      throw error;
    }
  }

  async restoreProduct(id: string): Promise<void> {
    try {
      const response = await fetch("/api/products/restore", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id }),
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Error restoring product");
      }
    } catch (error) {
      console.error("Error restoring product:", error);
      throw error;
    }
  }

  async deleteProduct(id: string): Promise<void> {
    try {
      // Use API endpoint to delete product (handles image cleanup)
      const response = await fetch(
        `/api/products/delete?id=${encodeURIComponent(id)}`,
        {
          method: "DELETE",
        },
      );
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Error deleting product");
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      throw error;
    }
  }

  async getProduct(
    id: string,
    includeArchived: boolean = true,
  ): Promise<Product> {
    // Use API endpoint to get product by id
    const params = new URLSearchParams();
    params.append("id", id);
    params.append("includeArchived", String(includeArchived));
    const response = await fetch(`/api/products?${params.toString()}`);
    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.error || "Error fetching product");
    }
    return result.data;
  }

  async getProductVariants(productId: string): Promise<ProductVariant[]> {
    try {
      const params = new URLSearchParams();
      params.append("productId", productId);
      const response = await fetch(
        `/api/products/variants?${params.toString()}`,
      );
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Error fetching product variants");
      }
      return result.variants || [];
    } catch (error) {
      console.error("Error in getProductVariants:", error);
      throw error;
    }
  }

  async searchProductsByName(
    name: string,
    includeArchived: boolean = false,
  ): Promise<Product[]> {
    const params = new URLSearchParams();
    params.append("name", name);
    params.append("includeArchived", String(includeArchived));
    const response = await fetch(`/api/products/search?${params.toString()}`);
    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.error || "Error searching products by name");
    }
    return result.products || [];
  }

  async listProducts(options?: {
    category?: string;
    subcategory?: string;
    type?: "external" | "internal";
    origin_location?: "UK" | "Ghana";
    condition?: "New" | "Used";
    limit?: number;
    offset?: number;
    includeArchived?: boolean;
  }): Promise<{ data: Product[]; count: number }> {
    const params = new URLSearchParams();
    if (options?.category) params.append("category", options.category);
    if (options?.subcategory) params.append("subcategory", options.subcategory);
    if (options?.type) params.append("type", options.type);
    if (options?.origin_location)
      params.append("origin_location", options.origin_location);
    if (options?.condition) params.append("condition", options.condition);
    if (options?.limit) params.append("limit", String(options.limit));
    if (options?.offset) params.append("offset", String(options.offset));
    params.append("includeArchived", String(options?.includeArchived ?? false));
    const response = await fetch(`/api/products/list?${params.toString()}`);
    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.error || "Error fetching products");
    }
    return { data: result.data || [], count: result.count || 0 };
  }
}
