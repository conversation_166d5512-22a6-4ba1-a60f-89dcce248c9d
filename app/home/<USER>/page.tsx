"use client";

import { useState, useEffect, useRef, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { ProductService } from "@/services/product.service";
import { Product } from "@/data/models/product.model";
import { categories, country_currencies } from "@/lib/constants";
import { useCurrency } from "@/components/app/CurrencyProvider";
import { AddressService } from "@/services/address.service";
import { ExchangeRateService } from "@/services/exchange.service";
import { ShopSkeleton } from "@/components/skeletons/shop";
import ReactMarkdown from "react-markdown";
import { Skeleton } from "@/components/ui/skeleton";
import {
  formatCurrencyWithDigits,
  formatCurrency,
  formatCurrencyFallback,
} from "@/lib/currency";

/**
 * Home / Shop page
 *
 * This file implements:
 *  - currency inference (address -> browser locale -> GBP)
 *  - exchange rate fetching
 *  - product listing (existing behavior)
 *  - a floating action button (FAB) that opens a nice country/currency selection dialog
 *    with emoji flags, quick search, and ability to revert to "Auto (inferred)".
 *
 * The dialog uses Radix Dialog wrapper components already available in the project.
 */

/* --- Types --- */
type Banner = {
  title: string;
  subtitle: string;
  productName: string;
  cta1: string;
  cta2: string;
  bgColor: string;
  textColor: string;
  image: string;
};

type Category = {
  id: string;
  name: string;
  parent_id: string | null;
};

type CategoryHierarchy = {
  id: string;
  name: string;
  children: CategoryHierarchy[];
};

// Component that uses useSearchParams (needs to be in Suspense)
function ProductsContent() {
  // Get search params to read category from URL
  const searchParams = useSearchParams();
  
  // Category hierarchy utility functions
  const buildCategoryHierarchy = (
    categories: Category[],
  ): CategoryHierarchy[] => {
    const categoryMap = new Map<string, CategoryHierarchy>();
    const rootCategories: CategoryHierarchy[] = [];

    // First pass: create all category objects
    categories.forEach((category) => {
      categoryMap.set(category.id, {
        id: category.id,
        name: category.name,
        children: [],
      });
    });
/* --- Helpers / constants --- */

// Storage keys for session-level preference
const STORAGE_KEY = "mp_currency";
const STORAGE_SOURCE = "mp_currency_source";

// Currency flags are derived from the canonical mapping in lib/constants via
// the `currencyCodeToFlag` helper which converts currency -> representative
// country code -> emoji flag.

export default function Products() {
  // Category helpers
  const buildCategoryHierarchy = (cats: Category[]): CategoryHierarchy[] => {
    const map = new Map<string, CategoryHierarchy>();
    const roots: CategoryHierarchy[] = [];
    cats.forEach((c) =>
      map.set(c.id, { id: c.id, name: c.name, children: [] }),
    );
    cats.forEach((c) => {
      const node = map.get(c.id)!;
      if (c.parent_id === null) roots.push(node);
      else {
        const parent = map.get(c.parent_id);
        if (parent) parent.children.push(node);
      }
    });
    return roots;
  };

  const getRootCategories = (): Category[] =>
    categories.filter((c) => c.parent_id === null);

  // UI state
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(),
  );
  const [activeCategoryId, setActiveCategoryId] = useState<string | null>(null);

  // Read category from URL parameters on component mount
  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      // Validate that the category exists in our categories list
      const categoryExists = categories.find(cat => cat.id === categoryParam);
      if (categoryExists) {
        setActiveCategoryId(categoryParam);
      }
    }
  }, [searchParams]);

  // State for banner
  // Banner / carousel state (kept for feature parity)
  const [currentBanner, setCurrentBanner] = useState(0);
  const autoScrollTimer = useRef<NodeJS.Timeout | null>(null);
  const autoScrollInterval = 5000;
  const banners: Banner[] = [
    {
      title: "Redefine Your Style Journey",
      subtitle: "Crafted With Intention",
      productName: "MailPallet",
      cta1: "View Product",
      cta2: "Shop Products Now",
      bgColor: "bg-black",
      textColor: "text-white",
      image:
        "https://placehold.co/500x600",
    },
  ];

  useEffect(() => {
    const next = () =>
      setCurrentBanner((p) => (p === banners.length - 1 ? 0 : p + 1));
    autoScrollTimer.current = setInterval(next, autoScrollInterval);
    return () => {
      if (autoScrollTimer.current) clearInterval(autoScrollTimer.current);
    };
  }, [banners.length]);

  // Products & currency state
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  // Currency state is provided by CurrencyProvider (useCurrency)
  const { currencyCode, currencySource, exchangeRate, isLoadingExchangeRate } =
    useCurrency();

  // Keep legacy variable name used by rendering logic for minimal changes
  const userCurrencyCode = currencyCode;

  // Pricing helpers
  const getDisplayPricing = (product: Product) => {
    if (product.variants?.variants && product.variants.variants.length > 0) {
      const first = product.variants.variants[0];
      return {
        price: first.price || product.primary_data.price,
        sale_price: first.sale_price || product.primary_data.sale_price,
        hasVariants: true,
      };
    }
    return {
      price: product.primary_data.price,
      sale_price: product.primary_data.sale_price,
      hasVariants: false,
    };
  };

  const convertPrice = (price: number) => {
    if (userCurrencyCode === "GBP" || !exchangeRate) return price;
    return price * exchangeRate;
  };

  // Exchange rate fetching and caching is handled by CurrencyProvider.
  // The listing consumes provider values via `useCurrency()` above.
  //
  // Centralized formatting helper:
  // Use the digits-explicit helper to preserve the original zero-decimal visual
  // behaviour while still preferring known local symbols (e.g. ₦, ₵).
  // For cases where exact digits other than 0 are required, use
  // `formatCurrencyWithDigits(...)` directly.
  const formatCurrency = (amount: number, fromCurrency: string) => {
    try {
      // Force 0 fraction digits for listing display (legacy UX).
      return formatCurrencyWithDigits(amount, fromCurrency, 0);
    } catch {
      // Conservative fallback
      return formatCurrencyFallback(amount, fromCurrency);
    }
  };

  // Currency lookups and inference are handled centrally by CurrencyProvider.

  // Browser locale inference handled by CurrencyProvider (no local inference here).

  // Currency bootstrapping is handled by CurrencyProvider. No local bootstrap logic in the listing page.

  // Exchange rate updates are handled by CurrencyProvider; no local fetch effect required.

  // Fetch products listing (unchanged logic)
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const productService = new ProductService();
        const selectedCategory = categories.find(
          (c) => c.id === activeCategoryId,
        );
        const options = activeCategoryId
          ? selectedCategory && selectedCategory.parent_id
            ? { subcategory: activeCategoryId }
            : { category: activeCategoryId }
          : {};
        const { data } = await productService.listProducts(options);
        setProducts(data);
      } catch (err) {
        console.error("Error fetching products:", err);
      } finally {
        setLoading(false);
      }
    };
    fetchProducts();
  }, [activeCategoryId]);

  // Stock helper
  const getStockStatus = (product: Product) => {
    if (
      product.type === "internal" &&
      product.details &&
      "stock" in product.details &&
      typeof product.details.stock === "number"
    ) {
      const stockLevel = product.details.stock;
      if (stockLevel > 10)
        return { text: "In Stock", classes: "bg-green-100 text-green-800" };
      if (stockLevel > 0)
        return {
          text: `Only ${stockLevel} left`,
          classes: "bg-yellow-100 text-yellow-800",
        };
      return { text: "Out of Stock", classes: "bg-red-100 text-red-800" };
    }
    return null;
  };

  return (
    <>
      {/* Full-width Hero Banner - Desktop Only */}
      <div className="hidden md:block w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw] mb-8 md:mb-12">
        <div 
          className="relative h-96 lg:h-[500px] overflow-hidden bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url(${banners[currentBanner].image})`,
          }}
        >
          {/* Dark overlay for better text readability */}
          <div className="absolute inset-0 bg-black bg-opacity-60"></div>
          
          <div className="max-w-7xl mx-auto px-6 md:px-8 lg:px-12 h-full flex items-center relative z-10">
            <div className="w-full text-center lg:text-left">
              <div className="space-y-6 max-w-2xl mx-auto lg:mx-0">
                <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-white leading-tight drop-shadow-lg">
                  {banners[currentBanner].title}
                  <br />
                  <span className="text-blue-300">{banners[currentBanner].subtitle}</span>
                </h1>
                
                <p className="text-xl lg:text-2xl text-gray-200 font-medium drop-shadow-md">
                  {banners[currentBanner].productName}
                </p>
              </div>
            </div>
          </div>

          {/* Banner Navigation Dots */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => handleBannerClick(index)}
                className={`h-3 rounded-full transition-all duration-300 shadow-md ${
                  currentBanner === index 
                    ? "bg-white w-8" 
                    : "bg-white bg-opacity-50 hover:bg-opacity-75 w-3"
                }`}
                aria-label={`Go to banner ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-3 sm:px-4 py-6 sm:py-8 lg:px-8">

      {/* Mobile Horizontal Scrollable Categories - Improved spacing */}
      <div className="md:hidden mb-4 overflow-x-auto scrollbar-hide">
        <div className="flex space-x-2 sm:space-x-4 py-2 px-1 min-w-full">
          {/* All Products button */}
          <button
            className={`flex-shrink-0 py-1 sm:py-2 px-3 sm:px-4 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
              activeCategoryId === null
                ? "bg-gray-900 text-white"
                : "bg-gray-100 text-gray-800 hover:bg-gray-200"
            }`}
            onClick={() => handleCategorySelect(null)}
          >
            All Products
          </button>
          {/* Category buttons */}
          {rootCategories.map((category) => (
            <button
              key={category.id}
              className={`flex-shrink-0 py-1 sm:py-2 px-3 sm:px-4 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap transition-colors ${
                activeCategoryId === category.id
                  ? "bg-gray-900 text-white"
                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
              }`}
              onClick={() => handleCategorySelect(category.id)}
            >
              {category.name}
            </button>
          ))}
  /* --- Currency dialog & FAB handlers --- */

  /* --- Category helpers used in markup --- */
  const toggleCategoryExpansion = (id: string) => {
    const next = new Set(expandedCategories);
    if (next.has(id)) next.delete(id);
    else next.add(id);
    setExpandedCategories(next);
  };

  const handleCategorySelect = (id: string | null) => {
    setActiveCategoryId(id);
  };

  /* --- Render --- */
  const rootCategories = getRootCategories();
  const categoryHierarchy = buildCategoryHierarchy(categories);

  // Filter helper for dialog search

  return (
    <main className="max-w-7xl mx-auto px-3 sm:px-4 py-6 sm:py-8 lg:px-8 relative">
      {/* Header with currency indicator */}
      <div className="flex items-center mb-0">
        <div>
          <h2 className="text-lg font-semibold">Shop</h2>
          <p className="text-sm text-gray-500">Browse our products</p>
        </div>
      </div>

      {/* Product listing / categories left column */}
      <div className="flex flex-col md:flex-row gap-8">
        <div className="hidden md:block w-full md:w-64 flex-shrink-0">
          <nav className="space-y-4">
            <div className="pb-2">
              <button
                onClick={() => handleCategorySelect(null)}
                className={`block py-2 font-medium transition-colors w-full text-left ${
                  activeCategoryId === null
                    ? "text-blue-600"
                    : "text-gray-900 hover:text-gray-600"
                }`}
              >
                All Products
              </button>
            </div>

            <div className="pb-2 border-b">
              <div className="py-2 font-medium">
                <span>Categories</span>
              </div>
            </div>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {categoryHierarchy.map((cat) => (
                <div key={cat.id} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleCategorySelect(cat.id)}
                      className={`flex-1 text-left py-1 font-medium transition-colors ${
                        activeCategoryId === cat.id
                          ? "text-blue-600"
                          : "text-gray-900 hover:text-gray-600"
                      }`}
                    >
                      {cat.name}
                    </button>
                    {cat.children.length > 0 && (
                      <button
                        onClick={() => toggleCategoryExpansion(cat.id)}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <svg
                          className={`w-3 h-3 transition-transform ${expandedCategories.has(cat.id) ? "rotate-90" : ""}`}
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                        >
                          <path
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M9 5l7 7-7 7"
                          ></path>
                        </svg>
                      </button>
                    )}
                  </div>

                  {expandedCategories.has(cat.id) &&
                    cat.children.length > 0 && (
                      <div className="pl-4 space-y-1">
                        {cat.children.map((sub) => (
                          <button
                            key={sub.id}
                            onClick={() => handleCategorySelect(sub.id)}
                            className={`flex items-center gap-2 py-1 w-full text-left transition-colors ${
                              activeCategoryId === sub.id
                                ? "text-blue-600"
                                : "text-gray-700 hover:text-gray-600"
                            }`}
                          >
                            <span className="text-gray-400">•</span>
                            <span className="text-sm">{sub.name}</span>
                          </button>
                        ))}
                      </div>
                    )}
                </div>
              ))}
            </div>
          </nav>
        </div>

        <div className="flex-1">
          {loading ? (
            <ShopSkeleton />
          ) : products.length === 0 ? (
            <div className="py-12 text-center text-gray-500 h-screen">
              No products returned
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {products.map((product) => {
                const stockStatus = getStockStatus(product);
                const pricing = getDisplayPricing(product);
                return (
                  <Link
                    key={product.id}
                    href={`/home/<USER>/products/${product.id}`}
                    className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden border border-gray-100 block"
                  >
                    <div className="relative">
                      {/* Discount badge */}
                      {(() => {
                        const discountPercent =
                          pricing.sale_price &&
                          pricing.sale_price < pricing.price
                            ? Math.round(
                                ((pricing.price - pricing.sale_price) /
                                  pricing.price) *
                                  100,
                              )
                            : 0;
                        return discountPercent > 0 ? (
                          <div className="absolute top-2 left-2 z-10 bg-red-500 text-white text-xs font-bold py-1 px-2 rounded-full">
                            {discountPercent}% OFF
                          </div>
                        ) : null;
                      })()}

                      <div
                        className={`absolute top-2 right-2 z-10 text-xs font-bold py-1 px-2 rounded-full shadow-sm ${product.origin_location === "UK" ? "bg-gradient-to-r from-blue-600 via-white to-red-600 text-white" : "bg-gradient-to-r from-red-600 via-yellow-500 to-green-600 text-white"}`}
                      >
                        {product.origin_location}
                      </div>

                      <div className="w-full h-48 sm:h-56 md:h-64 overflow-hidden">
                        <Image
                          src={
                            product.variants?.variants?.[0]?.images?.[0]?.url ||
                            product.primary_image?.url ||
                            "https://placehold.co/400x300"
                          }
                          alt={product.title}
                          width={400}
                          height={300}
                          className="w-full h-full object-contain object-center transform group-hover:scale-105 transition-transform duration-500"
                        />
                      </div>
                    </div>

                    <div className="p-3 sm:p-4">
                      <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-1 sm:mb-2 line-clamp-1 group-hover:text-blue-600 transition-colors">
                        {product.title}
                      </h3>

                      <div className="text-xs sm:text-sm text-gray-500 mb-3 sm:mb-4 line-clamp-2">
                        <ReactMarkdown className="prose prose-sm max-w-none dark:prose-invert">
                          {product.description}
                        </ReactMarkdown>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-end gap-1 sm:gap-2">
                          {pricing.sale_price &&
                          pricing.sale_price < pricing.price ? (
                            <>
                              {isLoadingExchangeRate &&
                              userCurrencyCode !== "GBP" ? (
                                <>
                                  <Skeleton className="w-24 h-6" />
                                  <Skeleton className="w-20 h-4" />
                                </>
                              ) : (
                                <>
                                  <span className="text-lg sm:text-xl font-bold text-gray-900">
                                    {formatCurrencyWithDigits(
                                      convertPrice(pricing.sale_price),
                                      userCurrencyCode,
                                      0,
                                    )}
                                  </span>
                                  <span className="text-sm sm:text-base text-gray-500 line-through">
                                    {formatCurrencyWithDigits(
                                      convertPrice(pricing.price),
                                      userCurrencyCode,
                                      0,
                                    )}
                                  </span>
                                </>
                              )}
                            </>
                          ) : isLoadingExchangeRate &&
                            userCurrencyCode !== "GBP" ? (
                            <Skeleton className="w-24 h-6" />
                          ) : (
                            <span className="text-lg sm:text-xl font-bold text-gray-900">
                              {formatCurrencyWithDigits(
                                convertPrice(pricing.price),
                                userCurrencyCode,
                                0,
                              )}
                            </span>
                          )}
                        </div>

                        {stockStatus && (
                          <span
                            className={`text-xs px-2 py-1 rounded ${stockStatus.classes}`}
                          >
                            {stockStatus.text}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </main>
    </>
  );
}

// Main component with Suspense boundary
export default function Products() {
  return (
    <Suspense fallback={<ShopSkeleton />}>
      <ProductsContent />
    </Suspense>
  );
}
