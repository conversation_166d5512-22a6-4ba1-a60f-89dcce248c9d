export interface Product {
    id: string;
    title: string;
    description: string;
    primary_image: { url: string; name: string; };
    shared_images: Array<{ url: string; name: string; }>;
    primary_data: ProductPrimaryData;
    currency: string;
    type: "external" | "internal";
    category: string;
    subcategory: string;
    brand?: string;
    tags?: string[];
    condition: "New" | "Used";
    admin_notes?: string;
    created_at: Date;
    updated_at: Date;
    origin_location: "UK" | "Ghana";
    shipping_rates: ProductShippingRate[];
    details: ExternalProductDetails | InternalProductDetails;
    archived?: boolean;
    refundable: boolean;
    refund_policy: string;
    variants?: ProductVariant;
}

export interface ProductPrimaryData {
    sale_price: number;
    price: number;
    discount_percent?: number;
}
export interface ProductVariantItem {
    option_values: Record<string, string>;
    sale_price?: number;
    price?: number;
    stock?: number;
    images?: Array<{ url: string; name: string; }>;
    variant_id?: string;
    [key: string]: any; // Allow additional properties
}

export interface ProductVariant {
    variants: Array<ProductVariantItem>;
    master_options: Array<Record<string, Array<string>>>;
}

export interface ProductShippingRate {
    to_country: "UK" | "Ghana" | "Nigeria" | "Rest Of Africa";
    to_zone: "UK" | "Accra" | "Outside Accra" | "Lagos" | "Outside Lagos";
    base_rate?: number;
    duty?: number;
    rate_per_kg?: number;
    estimated_delivery_days?: number;
    currency: string;
}

export interface ExternalProductDetails {
    origin_name: string;
    origin_url: string;
    is_affiliate: boolean;
    discount_code?: string;
}

export interface InternalProductDetails {
    location: "UK" | "Ghana"; 
    weight_kg?: number;  
    dimensions_cm?: { 
      length?: number;
      width?: number;
      height?: number;
    }; 
}

// Interface for creating a product with variants
export interface ProductCreationData {
  product: Omit<Product, "id" | "created_at" | "updated_at">;
  primaryImage: File;
  shared_images?: File[];
  variant_images?: { [variantIndex: number]: File[] };
}

// Interface for updating a product with variants
export interface ProductUpdateData {
  productUpdates?: Partial<Product>;
  primaryImage?: File;
  shared_images?: File[];
  variant_images?: { [variantIndex: number]: File[] };
  // Names of existing images to remove from storage and DB
  removed_shared_image_names?: string[];
  removed_variant_image_names?: string[];
}