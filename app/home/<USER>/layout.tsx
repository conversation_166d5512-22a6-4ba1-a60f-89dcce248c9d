import Footer from "@/components/index/footer";
import CurrencyProvider from "@/components/app/CurrencyProvider";
import dynamic from "next/dynamic";

const CurrencyFab = dynamic(() => import("@/components/app/CurrencyFab"), {
  ssr: false,
});

export default function ShopLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Main content area */}
      <div className="flex-1">
        <CurrencyProvider>
          {children}
          <CurrencyFab />
        </CurrencyProvider>
      </div>
      {/* Footer */}
      <Footer />
    </div>
  );
}
