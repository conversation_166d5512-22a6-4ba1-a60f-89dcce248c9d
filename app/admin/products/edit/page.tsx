"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation"; // For redirecting
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Search, ShoppingBag, DollarSign, Tag, ImageIcon, Archive, Edit3, AlertTriangle, Package, Truck, Trash2, Undo2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem, SelectGroup } from "@/components/ui/select";
import { ProductService } from "@/services/product.service.ts";
import { Product, ExternalProductDetails, InternalProductDetails, ProductShippingRate } from "@/data/models/product.model";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const EditProductPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFiltering, setIsFiltering] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [products, setProducts] = useState<Product | Product[] | null>(null);
  const [searchAttempted, setSearchAttempted] = useState<boolean>(false);
  const [selectedFilter, setSelectedFilter] = useState<string>("");
  const productService = new ProductService();
  const router = useRouter();
  // UUID v4 regex
  const isUUID = (str: string) => {
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    return uuidRegex.test(str);
  };

  const getProductDetails = async (idOrName: string): Promise<Product | Product[] | null> => {
    if (!idOrName.trim()) {
      toast({
        title: "Search Error",
        description: "Search term cannot be empty.",
        variant: "destructive",
      });
      return null;
    }
    try {
      let fetchedProduct: Product | null = null;
      let fetchedProducts: Product[] = [];
      if (isUUID(idOrName)) {
        fetchedProduct = await productService.getProduct(idOrName);
        if (!fetchedProduct) {
          toast({
            title: "Not Found",
            description: `No product found with ID: ${idOrName}`,
            variant: "default",
          });
        }
      } else {
        const productsByName = await productService.searchProductsByName(idOrName, true);
        if (productsByName.length > 0) {
          fetchedProducts = productsByName;
        } else {
          toast({
            title: "Not Found",
            description: `No product found with name: ${idOrName}`,
            variant: "default",
          });
        }
      }
      return fetchedProduct ?? fetchedProducts;
    } catch (error: any) {
      console.error("Error fetching product details:", error);
      toast({
        title: "Fetch Error",
        description: `Failed to fetch product details: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }
  };

  const handleFetchProductDetails = async (searchTermValue: string) => {
    if (!searchTermValue.trim()) {
      setProducts(null); // Clear previous product if search term is empty
      setSearchAttempted(false);
      return;
    }
    setSearchAttempted(true);
    setIsLoading(true);
    const fetchedProduct = await getProductDetails(searchTermValue);
    setProducts(fetchedProduct);
    setIsLoading(false);
  };

  // Function to apply the filter
  const applyFilter = async (filterValue: string) => {
    if(!filterValue) return;
    setIsFiltering(true);
    let filteredProducts: Product | Product[] | null = null;
    const products = await productService.listProducts();
    if (filterValue === "all") {
      filteredProducts = products.data;
    } else if (filterValue === "no-shipping") {
      filteredProducts = products.data.filter((product) => product.shipping_rates.some((rate) => rate.base_rate === 0));
    }
    setProducts(filteredProducts);
    setIsFiltering(false);
  };

  const handleArchiveProduct = async (id: string) => {
    setIsLoading(true);
    try {
      await productService.archiveProduct(id);
      toast({
        title: "Archive Successful",
        description: "Product has been archived successfully.",
        variant: "default",
      });
      // Clear the form and product state
      setSearchTerm("");
      setProducts(null);
      setSearchAttempted(false);
    } catch (error: any) {
      console.error("Error archiving product:", error);
      toast({
        title: "Archive Failed",
        description: `Error archiving product: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProduct = async (id: string) => {
    setIsLoading(true);
    try {
      await productService.deleteProduct(id);
      toast({
        title: "Delete Successful",
        description: "Product has been permanently deleted.",
        variant: "default",
      });
      // Clear the form and product state
      setSearchTerm("");
      setProducts(null);
      setSearchAttempted(false);
    } catch (error: any) {
      console.error("Error deleting product:", error);
      toast({
        title: "Delete Failed",
        description: `Error deleting product: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestoreProduct = async (id: string) => {
    setIsLoading(true);
    try {
      await productService.restoreProduct(id);
      toast({
        title: "Restore Successful",
        description: "Product has been restored successfully.",
        variant: "default",
      });
      // Clear the form and product state
      setSearchTerm("");
      setProducts(null);
      setSearchAttempted(false);
    } catch (error: any) {
      console.error("Error restoring product:", error);
      toast({
        title: "Restore Failed",
        description: `Error restoring product: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleNavigateToEdit = (id: string) => {
    router.push(`/admin/products/edit/${id}`);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
        <p className="text-muted-foreground">Search for a products by ID or Name to view and manage its details.</p>
      </div>

      {/* Search Box */}
      <Card className={"animate-in fade-in slide-in-from-bottom duration-300"}>
        <CardContent className="pt-6">
          <div className="flex gap-4 items-center"> {/* Added items-center to align search and select */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Enter Product ID or Name..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  if (e.target.value === "") {
                    setProducts(null);
                    setSearchAttempted(false);
                  }
                }}
                onKeyUp={async (e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === "Enter") {
                    await handleFetchProductDetails(searchTerm);
                  }
                }}
              />
            </div>
            <Button
              onClick={async () => {
                await handleFetchProductDetails(searchTerm);
              }}
              disabled={isLoading}
            >
              {isLoading ? "Searching..." : "Search Product"}
            </Button>

            {/* Product Filter */}
            <Select value={selectedFilter} onValueChange={(value) => {
              setSelectedFilter(value as string);
              applyFilter(value as string);
            }}>
              <SelectTrigger className="w-[200px]"> {/* Adjust width as needed */}
                <SelectValue placeholder="Filter Products" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="all">All Products</SelectItem>
                  <SelectItem value="no-shipping">No Shipping Rates</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Product Details Section - To be implemented in Goal 3 */}
      {isLoading || isFiltering && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center">Loading product details...</p>
          </CardContent>
        </Card>
      )}
      {!isLoading && !isFiltering && products && Array.isArray(products) && products.length > 0 && (
        <div>
          {products.map((product) => (
            <Card className="animate-in fade-in slide-in-from-bottom duration-300 mt-2" key={product.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="h-5 w-5" />
                    Product Details: {product.title}
                    {product.archived && (
                      <Badge variant="outline" className="ml-2 border-orange-500 text-orange-600">
                        Archived
                      </Badge>
                    )}
                  </CardTitle>
                  <Badge variant={product.condition === "New" ? "default" : "secondary"}>{product.condition}</Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Info */}
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-1">
                    <label className="text-sm text-muted-foreground">Product ID</label>
                    <p className="font-medium">{product.id}</p>
                  </div>
                  <div className="space-y-1 md:col-span-2">
                    <label className="text-sm text-muted-foreground">Title</label>
                    <p className="font-medium">{product.title}</p>
                  </div>
                </div>
                <div className="grid gap-4 md:grid-cols-3">
                  {/*<div className="space-y-1">*/}
                  {/*  <label className="text-sm text-muted-foreground">Description</label>*/}
                  {/*  <p className="font-medium whitespace-pre-wrap">{product.description}</p>*/}
                  {/*</div>*/}
                  <div className="space-y-1">
                    <label className="text-sm text-muted-foreground">Location</label>
                    <p className="font-medium">{(product.details as InternalProductDetails).location}</p>
                  </div>
                </div>
                <Separator />
                {/* Action Buttons */}
                <div className="flex gap-4">
                  {/* Archive Button - Only show if product is not archived */}
                  {!product.archived && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" className="gap-2 border-orange-500 text-orange-600 hover:bg-orange-50 hover:text-orange-700" disabled={isLoading}>
                          <Archive className="h-4 w-4" />
                          Archive Product
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Archive Product?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will archive the product &quot;{product.title}&quot; and hide it from all product listings. The product can be restored later if needed. No data will be
                            permanently deleted.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleArchiveProduct(product.id)} className="bg-orange-500 text-white hover:bg-orange-600">
                            Archive
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}

                  {/* Restore and Delete Buttons - Only show if product is archived */}
                  {product.archived && (
                    <>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" className="gap-2 border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700" disabled={isLoading}>
                            <Undo2 className="h-4 w-4" />
                            Restore Product
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Restore Product?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will restore the product &quot;{product.title}&quot; and make it visible in all product listings again. The product will be available for customers to view
                              and purchase.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleRestoreProduct(product.id)} className="bg-green-500 text-white hover:bg-green-600">
                              Restore
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" className="gap-2" disabled={isLoading}>
                            <Trash2 className="h-4 w-4" />
                            Delete Product
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Permanently Delete Product?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will permanently delete the product &quot;{product.title}&quot; from the database. This action cannot be undone. All data associated with this product will
                              be lost forever.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteProduct(product.id)} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                              Delete Permanently
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </>
                  )}

                  <Button onClick={() => handleNavigateToEdit(product.id)} className="gap-2">
                    <Edit3 className="h-4 w-4" />
                    Edit Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      {!isLoading && !isFiltering && !products && searchTerm !== "" && searchAttempted && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">No product found for: {searchTerm}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EditProductPage;
