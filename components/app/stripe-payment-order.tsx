"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { loadStripe } from "@stripe/stripe-js";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Check as CheckIcon, X as XIcon } from "lucide-react";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Elements, PaymentElement, useStripe, useElements } from "@stripe/react-stripe-js";
import type { OrderItem, Currency, ShippingAddress } from "@/data/models/order.model";
import { AddressService, type UserAddressModel } from "@/services/address.service";
import { UserService } from "@/services/user.service";
import { getOrderSuccessful } from "@/lib/email";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const API_URL = process.env.NEXT_PUBLIC_SUPABASE_FUNCTIONS_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

interface PaymentOrderProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  userId: string;
  email: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  currency: Currency;
}

interface OrderCheckoutFormProps {
  userId: string;
  email: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  totalAmount: number;
  currency: Currency;
  onClose: () => void;
  onSuccess: (orderId: string, receiptUrl: string) => void;
  onError: (message: string) => void;
  onDiscountApplied: (discountAmount: number) => void;
}

// The actual checkout form component
function OrderCheckoutForm({
  userId,
  email,
  items,
  subtotal,
  taxAmount,
  shippingCost,
  totalAmount,
  currency,
  onClose,
  onSuccess,
  onError,
  onDiscountApplied,
}: OrderCheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [discountCode, setDiscountCode] = useState("");
  const [discountError, setDiscountError] = useState("");
  const [discountAmount, setDiscountAmount] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [appliedDiscount, setAppliedDiscount] = useState(false);
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false);
  const [shippingAddress, setShippingAddress] = useState<UserAddressModel>({});
  const [isLoadingAddress, setIsLoadingAddress] = useState(true);

   const userService = new UserService();

  // Fetch user's shipping address on component mount
  useEffect(() => {
    const fetchAddress = async () => {
      try {
        const addressService = new AddressService();
        const address = await addressService.fetchUserAddress();
        setShippingAddress(address);
      } catch (error) {
        console.error('Error fetching user address:', error);
        // Set empty address object if fetch fails
        setShippingAddress({});
      } finally {
        setIsLoadingAddress(false);
      }
    };

    fetchAddress();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    // Wait for address to load before processing
    if (isLoadingAddress) {
      onError("Please wait while we load your shipping address...");
      return;
    }

    setIsProcessing(true);

    try {
      const currentAmount = totalAmount - discountAmount;

      const { error: submitError } = await elements.submit();
      if (submitError) {
        onError(submitError.message ?? "An error occurred");
        return;
      }

      const response = await fetch(`${API_URL}/payment-order`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          userId,
          email,
          items,
          subtotal,
          taxAmount,
          shippingCost,
          totalAmount: currentAmount,
          currency,
          discountCode: discountCode,
          isDiscounted: appliedDiscount,
          originalAmount: totalAmount.toString(),
          discountAmount: discountAmount.toString(),
        }),
      });

      const { clientSecret } = await response.json();

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret,
        redirect: "if_required",
      });

      if (error) {
        onError(error.message ?? "An error occurred");
      } else if (paymentIntent.status === "succeeded") {
        try {
          // Transform UserAddressModel to ShippingAddress format
          const transformedShippingAddress: ShippingAddress = {
            id: shippingAddress.uuid || `addr_${userId}_${Date.now()}`,
            recipient_name: `${shippingAddress.FirstName || ''} ${shippingAddress.LastName || ''}`.trim(),
            company_name: shippingAddress.Company || undefined,
            address_line_1: shippingAddress.Street || shippingAddress.Address || '',
            address_line_2: undefined, // UserAddressModel doesn't have address_line_2
            city: shippingAddress.City || '',
            state_province: shippingAddress.StateProvince || undefined,
            postal_code: shippingAddress.PostalCode || '',
            country: shippingAddress.DeliverTo!,
            phone_number: undefined, // UserAddressModel doesn't have phone_number
            delivery_instructions: shippingAddress.DeliverTo || undefined,
            is_residential: true // Default to residential
          };

          // const variable for email
          const htmlValues = {
            firstName: shippingAddress.FirstName,
            lastName: shippingAddress.LastName,
            items
          }


          const orderResponse = await fetch(`${API_URL}/create-order`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
            },
            body: JSON.stringify({
              userId,
              email,
              items,
              subtotal,
              taxAmount,
              shippingCost,
              totalAmount: currentAmount,
              currency,
              paymentIntentId: paymentIntent.id,
              discountAmount,
              originalAmount: totalAmount,
              shipping_address: transformedShippingAddress,
            }),
          });

          const orderData = await orderResponse.json();

          if (!orderResponse.ok || !orderData.success) {
            throw new Error(orderData.error || "Failed to create order");
          }

          // send email to customer
          const html = getOrderSuccessful(htmlValues.firstName!, htmlValues.lastName!, htmlValues.items);

          await userService.sendEmail(email, "Order Successful", html);

          onSuccess(orderData.orderId, orderData.receiptUrl);
        } catch (error) {
          console.error("Post-payment Error:", error);
          onError(error instanceof Error ? error.message : "An unexpected error occurred");
        };
      }
    } catch (error) {
      console.error("Payment Error:", error);
      onError("An unexpected error occurred");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApplyDiscount = async () => {
    setIsApplyingDiscount(true);
    setDiscountError("");

    try {
      const response = await fetch(`${API_URL}/validate-discount`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          code: discountCode.trim(),
          amount: totalAmount,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to validate discount code");
      }

      if (data.success) {
        setAppliedDiscount(true);
        setDiscountCode(data.discountCode);
        setDiscountAmount(data.discountAmount);
        onDiscountApplied(data.discountAmount);
      } else {
        setDiscountError(data.error || "Invalid discount code");
      }
    } catch (error) {
      console.error("Discount application error:", error);
      setDiscountError(error instanceof Error ? error.message : "Error processing discount code");
    } finally {
      setIsApplyingDiscount(false);
    }
  };

  const getDisplayAmount = () => {
    if (appliedDiscount) {
      return (totalAmount - discountAmount).toFixed(2);
    }
    return totalAmount.toFixed(2);
  };

  const getCurrencySymbol = () => {
    switch (currency) {
      case 'GBP': return '£';
      case 'USD': return '$';
      case 'GHS': return '₵';
      case 'NGN': return '₦';
      default: return '£';
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Discount Code"
            value={discountCode}
            onChange={(e) => setDiscountCode(e.target.value)}
            disabled={appliedDiscount || isProcessing}
            className="flex-1"
          />
          <Button type="button" variant="outline" onClick={handleApplyDiscount} disabled={!discountCode.trim() || appliedDiscount || isProcessing || isApplyingDiscount}>
            {isApplyingDiscount ? (
              <span className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Applying...
              </span>
            ) : (
              "Apply"
            )}
          </Button>
        </div>
        {discountError && <p className="text-sm text-red-500">{discountError}</p>}
        {appliedDiscount && <p className="text-sm text-green-500">Discount applied successfully! New total: {getCurrencySymbol()}{getDisplayAmount()}</p>}
      </div>

      <PaymentElement />
      <div className="flex gap-4">
        <Button type="button" variant="outline" onClick={onClose} className="flex-1" disabled={isProcessing}>
          Cancel
        </Button>
        <Button type="submit" className="flex-1" disabled={isProcessing || isLoadingAddress}>
          {isLoadingAddress ? (
            <span className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Loading address...
            </span>
          ) : isProcessing ? (
            <span className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Processing...
            </span>
          ) : (
            `Pay ${getCurrencySymbol()}${getDisplayAmount()}`
          )}
        </Button>
      </div>
    </form>
  );
}

// The main payment order modal component
export function StripePaymentOrder({
  isOpen,
  onClose,
  onSuccess,
  userId,
  email,
  items,
  subtotal,
  taxAmount,
  shippingCost,
  totalAmount,
  currency
}: PaymentOrderProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [clientSecret, setClientSecret] = useState<string>("");
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [orderId, setOrderId] = useState<string>("");
  const [receiptUrl, setReceiptUrl] = useState<string>("");
  const [discountedAmount, setDiscountedAmount] = useState(totalAmount);

  useEffect(() => {
    if (isOpen && totalAmount) {
      setIsLoading(true);
      fetch(`${API_URL}/payment-order`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          userId,
          email,
          items,
          totalAmount,
          currency
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.success) {
            setClientSecret(data.clientSecret);
          } else {
            throw new Error(data.error || "Failed to create payment intent");
          }
        })
        .catch((error) => {
          console.error("Payment Error:", error);
          setErrorMessage(error.message);
          setShowErrorDialog(true);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [isOpen, totalAmount, userId, email, items, currency]);

  const options = {
    clientSecret,
    appearance: {
      theme: "stripe" as const,
    },
  };

  const handlePaymentSuccess = (orderId: string, receiptUrl: string) => {
    setOrderId(orderId);
    setReceiptUrl(receiptUrl);
    setShowSuccessDialog(true);
    onClose();
  };

  const handlePaymentError = (message: string) => {
    onClose();
    setErrorMessage(message);
    setShowErrorDialog(true);
  };

  const handleDiscountApplied = (discountAmount: number) => {
    setDiscountedAmount(totalAmount - discountAmount);
  };



  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Complete Payment</DialogTitle>
          </DialogHeader>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </div>
          ) : clientSecret ? (
            <Elements stripe={stripePromise} options={options}>
              <OrderCheckoutForm
                userId={userId}
                email={email}
                items={items}
                subtotal={subtotal}
                taxAmount={taxAmount}
                shippingCost={shippingCost}
                totalAmount={discountedAmount}
                currency={currency}
                onClose={onClose}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onDiscountApplied={handleDiscountApplied}
              />
            </Elements>
          ) : null}
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Order Placed Successfully!</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="rounded-full bg-green-100 p-3">
              <CheckIcon className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-center text-gray-600">Your order has been placed and payment processed successfully.</p>
            <p className="text-center text-sm text-gray-500">Order ID: {orderId}</p>
            {receiptUrl && (
              <a href={receiptUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">
                View Receipt
              </a>
            )}
          </div>
          <Button
            onClick={() => {
              setShowSuccessDialog(false);
              if (onSuccess) {
                onSuccess();
              } else {
                router.refresh();
                window.location.reload();
              }
            }}
          >
            Continue
          </Button>
        </DialogContent>
      </Dialog>

      {/* Error Dialog */}
      <Dialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Order Failed</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="rounded-full bg-red-100 p-3">
              <XIcon className="h-6 w-6 text-red-600" />
            </div>
            <p className="text-center text-gray-600">{errorMessage || "There was an error processing your order."}</p>
          </div>
          <Button onClick={() => setShowErrorDialog(false)} variant="destructive">
            Close
          </Button>
        </DialogContent>
      </Dialog>
    </>
  );
}
